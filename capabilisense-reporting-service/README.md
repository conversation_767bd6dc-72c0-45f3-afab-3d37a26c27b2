# CapabiliSense PDF Reporting Service

A Go-based service that generates professional PDF reports for digital transformation capability assessments.

## 🚀 Current Status: v0.3.0 - Production-Ready with Caching & Optimization

✅ **Stage A: Data Extraction & Processing** (~13ms)
✅ **Stage B: AI Insight Generation** (~3 seconds)
✅ **Chart Generation: Spider Charts** (with PDF integration)
✅ **Combined API Service** (3-second end-to-end pipeline)
✅ **One-Pager Report Structure** (executive-appropriate)
✅ **Multi-Provider LLM Integration** (with intelligent caching)
✅ **Performance Optimization** (70% token reduction)
✅ **Database Caching** (automatic cache management)
✅ **Professional PDF Output** (with charts and Unicode support)

## 📋 Features

### 🔄 Complete Three-Stage Pipeline
- **Stage A**: Data extraction and processing from SQLite database
- **Stage B**: AI-powered insight generation with multi-provider LLM support
- **Charts**: Spider chart generation with variable domains and maturity levels

### 🤖 AI Integration
- **Multi-Provider LLM**: Azure OpenAI, Google Gemini, and other providers
- **Intelligent Caching**: Database-backed caching for AI insights (project_id + run_id)
- **Token Optimization**: 70% reduction in LLM token usage (65k → 19k tokens for business overview)
- **Document Heads**: Uses 1k character snippets instead of full assessment data
- **Parallel Processing**: All 11 LLM calls run simultaneously
- **Intelligent Fallbacks**: Graceful degradation when AI services unavailable
- **Comprehensive Logging**: All LLM calls logged to `logs/llm-calls.jsonl`
- **Advanced Debugging**: Debug mode with artifact preservation and detailed logging

### 📊 One-Pager Report Structure
- **Organization Name**: AI-generated based on assessment context
- **Business Summary**: Executive summary with key findings
- **Strength Domains**: Top 3 performing domains with insights
- **Weakness Domains**: Bottom 3 domains with improvement recommendations
- **AI Spotlight**: Key insights and business impact analysis
- **Focus Area**: AI-generated strategic focus (not hardcoded)

### 📈 Chart Generation
- **Spider Charts**: Support for 3-12 domains and 1-10 maturity levels
- **PDF Integration**: Charts automatically inserted into PDF reports
- **Multiple Themes**: Default, Professional, and High Contrast configurations
- **PNG Output**: High-quality images with customizable styling
- **Unicode Safe**: Proper text rendering without garbled characters
- **API Integration**: RESTful endpoints for generation and serving

### 🌐 RESTful API
- **Combined Service**: Single API hosting all three stages
- **CORS Support**: Web application integration ready
- **Health Monitoring**: Separate health checks for each stage
- **Comprehensive Documentation**: API info at root endpoint

## 🏗️ Architecture

The service follows a three-stage pipeline:

1. **Stage A - Data Extraction** (`pkg/dataextraction/`): Raw assessment data processing and algorithmic analysis
2. **Stage B - AI Insights** (`pkg/aiinsights/`): LLM-powered insight generation with structured outputs
3. **Stage C - PDF Generation** (`pkg/pdfgenerator/`): Professional PDF report creation

## 🚀 Quick Start

### Prerequisites
- Go 1.24+
- SQLite database with assessment data
- Optional: LLM API keys for AI insights (Azure OpenAI, Google Gemini)

### Running the Combined API Service

```bash
# Set environment variables (optional)
export DATABASE_PATH="./assessment_backend.db"
export AZURE_OPENAI_API_KEY="your-key-here"
export GCP_API_KEY="your-google-key-here"
export GOOGLE_API_KEY="your-google-key-here"

# Run the combined API
go run cmd/combined_api/main.go

# Run with debug mode enabled
DEBUG=true go run cmd/combined_api/main.go
```

The service will start on port 8081 with the following endpoints:

### 📊 Stage A - Data Extraction
- `GET /api/v1/report-data?project_id=<id>` - Extract assessment data
- `GET /health` - Stage A health check
- `GET /status` - Detailed Stage A status

### 🤖 Stage B - AI Insights
- `POST /api/v1/generate-insights` - Generate AI insights from Stage A data
- `GET /health-b` - Stage B health check
- `GET /stage-b` - Stage B service info

### 📈 Chart Generation
- `POST /api/v1/generate-chart` - Generate spider charts
- `GET /api/v1/charts` - List generated charts
- `GET /charts/<filename>` - Serve chart images

### 📄 Complete Pipeline
- `POST /api/v1/generate-complete-pdf?project_id=<id>` - Generate complete PDF report (Stage A + B + PDF)

### 🔍 Service Info
- `GET /` - Complete API documentation and examples

### Testing the Pipeline

```bash
# 1. Get Stage A data
curl 'http://localhost:8081/api/v1/report-data?project_id=hr' > stage_a.json

# 2. Generate Stage B insights
curl -X POST 'http://localhost:8081/api/v1/generate-insights' \
     -H 'Content-Type: application/json' \
     -d @stage_a.json > stage_b.json

# 3. Generate spider chart
curl -X POST 'http://localhost:8081/api/v1/generate-chart' \
     -H 'Content-Type: application/json' \
     -d '{"stage_a_data": <stage_a_output>, "chart_type": "spider"}' > chart.json

# 4. Generate complete PDF report (all stages)
curl -X POST 'http://localhost:8081/api/v1/generate-complete-pdf?project_id=ai' \
     --output report.pdf
```

## 🐛 Debug Mode

### Enabling Debug Mode

Set the `DEBUG` environment variable to enable comprehensive debugging:

```bash
DEBUG=true go run cmd/combined_api/main.go
```

### Debug Features

When debug mode is enabled, the system provides:

1. **Comprehensive LLM Logging**: All LLM calls logged to `logs/llm-calls.jsonl`
2. **Debug Artifacts**: Input/output data saved to `logs/debug/`
3. **API Key Masking**: Sensitive information masked in logs
4. **Performance Metrics**: Token usage and processing time tracking
5. **Detailed Error Information**: Enhanced error messages and context

### Debug Artifacts

Debug artifacts are automatically saved when `DEBUG=true`:

```
logs/
├── llm-calls.jsonl                                  # All LLM request/response logs
└── debug/
    ├── stage_a_input_<project>_<timestamp>.json     # Stage A input data
    ├── stage_b_output_<project>_<timestamp>.json    # Stage B AI insights
    └── complete_pdf_<project>_<timestamp>.pdf       # Generated PDF reports
```

### Performance Metrics

The system has been heavily optimized for production performance:

- **Token Usage**: 70% reduction (65k → 19k tokens for business overview)
- **Processing Time**: ~3 seconds for complete pipeline (15x faster!)
- **Caching Benefits**: 32% faster on cache hits, significant cost savings
- **Parallel Processing**: All 11 LLM calls run simultaneously
- **Success Rate**: 100% completion rate with proper timeout handling
- **PDF Generation**: Professional output with charts in ~20ms

## 🗄️ Intelligent Caching System

The service includes a sophisticated caching system for optimal performance:

### Database-Backed Caching
- **AI Insights Cache**: `ai_insights_cache` table stores individual insights by type
- **Stage B Cache**: `stage_b_cache` table stores complete Stage B outputs
- **Cache Keys**: project_id + run_id + insight_type for precise cache management
- **Automatic Expiration**: Configurable cache expiration (currently no expiration)

### Cache Types
- **Business Overview**: Organization name + business summary (19k tokens saved)
- **Domain Insights**: Individual domain analysis and recommendations
- **AI Spotlight**: Strategic analysis and business impact
- **Focus Areas**: AI-driven priority recommendations

### Cache Performance
- **Cache Hit**: Instant retrieval, 100% token savings, 32% faster processing
- **Cache Miss**: Normal LLM processing + automatic cache storage
- **Debug Visibility**: Clear logging of cache hits/misses in debug mode
- **Cost Savings**: Significant reduction in LLM API costs for repeated requests

### Cache Management
```bash
# Monitor cache performance
grep "cached" logs/llm-calls.jsonl

# View cache hit/miss in debug mode
DEBUG=true go run cmd/combined_api/main.go
# Look for: [DEBUG] 📋 Using cached business overview (saved X tokens)
```

## 📊 Sample Report Content

The service generates comprehensive reports including:

- **Organization Overview**: Assessment summary and key metrics
- **Domain Scores**: Performance across capability domains (Vision, People, Process, Technology)
- **Key Strengths**: Top-performing areas with AI-generated insights
- **Areas for Focus**: Critical improvement opportunities
- **AI Spotlight**: Key insights and recommendations
- **ETS Solutions**: Specific solution recommendations

## 🔧 Configuration

### Environment Variables

- `PORT`: Service port (default: 8081)
- `PROMPTS_LIBRARY_PATH`: Path to prompts configuration (default: configs/prompts_library.json)
- `TEMPLATES_PATH`: Path to report templates (default: templates)
- `LOG_LEVEL`: Logging level (default: info)

### Mock Data

Currently uses comprehensive mock data for development and testing. See `getMockReportData()` in `cmd/reportgenerator/main.go`.

## 📁 Project Structure

```
capabilisense-reporting-service/
├── cmd/reportgenerator/           # Main application entry point
├── pkg/
│   ├── config/                    # Configuration management
│   ├── dataextraction/           # Data processing and analysis
│   ├── aiinsights/               # AI insights generation
│   ├── pdfgenerator/             # PDF rendering (dual approach)
│   └── models/                   # Shared data models
├── configs/                      # Configuration files
├── templates/                    # Report templates
└── docs/                        # Documentation
```

## 🔄 Development Status

### ✅ Completed
- Core service architecture
- Data extraction and processing algorithms
- Mock AI insights pipeline
- Simple PDF renderer with professional layout
- HTTP API endpoints
- Comprehensive error handling

### 🔄 Future Enhancements
- Real database integration for data extraction
- Live LLM integration (OpenAI, Google)
- Enhanced mdtopdf renderer debugging
- HTML templating with headless browser rendering
- Advanced report customization options

## 🐛 Known Issues & Troubleshooting

### ✅ Recently Fixed Issues:
- **Business Summary Prompt**: ✅ Fixed - Now returns proper executive summaries
- **Domain Insights Prompt**: ✅ Fixed - Professional analysis with structured output
- **Token Usage**: ✅ Optimized - 70% reduction through document heads approach
- **PDF Unicode**: ✅ Fixed - Proper bullet point rendering without garbled text
- **Chart Integration**: ✅ Fixed - Spider charts automatically inserted into PDFs
- **Performance**: ✅ Optimized - 3-second pipeline with parallel processing

### Current Known Issues:
- **Stage B Standalone API**: Returns 400 error due to data format mismatch (workaround: use complete pipeline)
- **mdtopdf Renderer**: Currently produces 0-byte PDFs due to library issues (Simple PDF Renderer provides reliable alternative)

### Quick Troubleshooting:

1. **Enable Debug Mode**: `DEBUG=true go run cmd/combined_api/main.go`
2. **Check LLM Logs**: `tail -f logs/llm-calls.jsonl`
3. **Use Complete Pipeline**: `curl -X POST 'http://localhost:8081/api/v1/generate-complete-pdf?project_id=ai' --output report.pdf`
4. **Verify API Keys**: Ensure `GCP_API_KEY` and `GOOGLE_API_KEY` are set

For detailed troubleshooting, see [`DEBUG_GUIDE.md`](DEBUG_GUIDE.md).

## 📚 Documentation

- [`CODEBASE.md`](CODEBASE.md) - Detailed codebase structure and architecture
- [`CHANGELOG_AND STATUS.md`](CHANGELOG_AND STATUS.md) - Development progress and status updates
- [`PRD.md`](PRD.md) - Product requirements and specifications
- [`TODO.md`](TODO.md) - Comprehensive task list and development roadmap
- [`DEBUG_GUIDE.md`](DEBUG_GUIDE.md) - Debug mode usage and troubleshooting guide

## 🤝 Contributing

1. Ensure Go 1.24+ is installed
2. Run tests: `go test ./...`
3. Follow Go conventions and add appropriate documentation
4. Test both PDF renderers when making changes

## 📚 Documentation

**📖 [Complete Documentation Index](docs/README.md)** - Navigate all documentation

### Core Documentation
- [LLM Library Documentation](docs/LLM_LIBRARY.md) - AI integration and optimization
- [Simple PDF Renderer](docs/SIMPLE_PDF_RENDERER.md) - PDF generation with charts
- [Intelligent Caching System](docs/CACHING.md) - Database-backed caching system
- [Debug Guide](docs/DEBUG_GUIDE.md) - Troubleshooting and monitoring
- [Testing Guide](docs/TESTING.md) - Test suite and validation

### Project Documentation
- [Product Requirements Document](docs/PRD.md) - Project specifications
- [Codebase Overview](docs/CODEBASE.md) - Architecture and structure
- [Changelog](docs/CHANGELOG.md) - Version history and updates
- [Task Summary](docs/TASK_SUMMARY.md) - Development progress
- [Test Suite Summary](docs/TEST_SUITE_SUMMARY.md) - Testing coverage
- [TODO](docs/TODO.md) - Future enhancements and roadmap

## 📄 License

[Add your license information here]