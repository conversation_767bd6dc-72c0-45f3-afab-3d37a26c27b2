package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	// Create output directory
	outputDir := "./adaptive-scaling-demo"
	if err := os.Mkdir<PERSON>ll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	fmt.Println("🎯 Testing Adaptive Scaling System")
	fmt.Println("Solving the 'white chart with little sticks' problem...")

	// Test scenarios
	testScenarios := []struct {
		name        string
		description string
		domains     []charts.DomainData
		filename    string
	}{
		{
			name:        "Very Low Scores",
			description: "All scores below 1.5 - should zoom and show red gradient",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 1.2},
				{Name: "Operations", Score: 0.8},
				{Name: "Technology", Score: 1.4},
				{Name: "People", Score: 0.9},
				{Name: "Governance", Score: 1.1},
			},
			filename: "01_very_low_scores.png",
		},
		{
			name:        "Low Scores",
			description: "All scores below 2.0 - should zoom moderately",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 1.8},
				{Name: "Operations", Score: 1.5},
				{Name: "Technology", Score: 1.9},
				{Name: "People", Score: 1.3},
				{Name: "Governance", Score: 1.7},
			},
			filename: "02_low_scores.png",
		},
		{
			name:        "Mixed Low-Medium",
			description: "Mix of low and medium scores - should zoom slightly",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 2.1},
				{Name: "Operations", Score: 1.4},
				{Name: "Technology", Score: 2.3},
				{Name: "People", Score: 1.8},
				{Name: "Governance", Score: 2.0},
			},
			filename: "03_mixed_low_medium.png",
		},
		{
			name:        "High Scores",
			description: "High scores - should NOT zoom (normal view)",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 4.2},
				{Name: "Operations", Score: 3.8},
				{Name: "Technology", Score: 4.5},
				{Name: "People", Score: 3.9},
				{Name: "Governance", Score: 4.1},
			},
			filename: "04_high_scores.png",
		},
		{
			name:        "Single Very Low Score",
			description: "One very low score among decent ones",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 3.5},
				{Name: "Operations", Score: 3.2},
				{Name: "Technology", Score: 0.8}, // Very low
				{Name: "People", Score: 3.1},
				{Name: "Governance", Score: 3.4},
			},
			filename: "05_single_low_score.png",
		},
		{
			name:        "Extreme Low Scores",
			description: "Extremely low scores (< 1.0) - maximum zoom",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 0.5},
				{Name: "Operations", Score: 0.3},
				{Name: "Technology", Score: 0.7},
				{Name: "People", Score: 0.4},
				{Name: "Governance", Score: 0.6},
			},
			filename: "06_extreme_low_scores.png",
		},
	}

	// Base chart configuration
	raysConfig := charts.CreateModernRaysConfig()

	// Adaptive scaling configuration
	adaptiveConfig := charts.DefaultAdaptiveScalingConfig()
	adaptiveConfig.ZoomThreshold = 2.5        // Zoom if max score < 2.5
	adaptiveConfig.ZoomFactor = 1.4           // Zoom to 1.4x the max score
	adaptiveConfig.MinZoomRange = 2.0         // Minimum zoom range
	adaptiveConfig.ShowUnusedRange = true     // Show red gradient
	adaptiveConfig.UnusedRangeIntensity = 0.7 // Strong red indication
	adaptiveConfig.ShowZoomIndicator = true   // Show zoom info

	fmt.Println("\n📊 Generating adaptive scaling examples...")

	for i, scenario := range testScenarios {
		fmt.Printf("\n%d. %s\n", i+1, scenario.name)
		fmt.Printf("   %s\n", scenario.description)

		// Create adaptive rays chart
		adaptiveChart := charts.NewAdaptiveRaysChart(raysConfig, adaptiveConfig)

		// Generate chart data
		chartData := charts.SpiderChartData{
			Domains:    scenario.domains,
			MaxLevel:   5, // Standard 1-5 scale
			ChartTitle: scenario.name,
		}

		// Generate chart
		outputPath := fmt.Sprintf("%s/%s", outputDir, scenario.filename)
		err := adaptiveChart.Generate(chartData, scenario.name, outputPath)
		if err != nil {
			log.Printf("   ✗ Error: %v", err)
			continue
		}

		// Calculate and display scaling info
		scaling := charts.CalculateAdaptiveScaling(chartData, adaptiveConfig)

		fmt.Printf("   ✓ Generated: %s\n", outputPath)
		fmt.Printf("   📈 Max Score: %.1f\n", scaling.OriginalMaxScore)
		if scaling.IsZoomed {
			fmt.Printf("   🔍 Zoomed: YES (0-%.1f of 0-5 scale)\n", scaling.ZoomedRange)
			fmt.Printf("   🔴 Red gradient: %.1f-5.0 range\n", scaling.ZoomedRange)
		} else {
			fmt.Printf("   🔍 Zoomed: NO (using full 0-5 scale)\n")
		}
	}

	// Generate comparison charts (with and without adaptive scaling)
	fmt.Println("\n🔄 Generating comparison: Adaptive vs Standard scaling...")

	// Use the very low scores scenario for comparison
	lowScoreData := charts.SpiderChartData{
		Domains:    testScenarios[0].domains,
		MaxLevel:   5,
		ChartTitle: "Comparison: Very Low Scores",
	}

	// Standard chart (no adaptive scaling)
	standardConfig := adaptiveConfig
	standardConfig.UseAdaptiveScaling = false
	standardChart := charts.NewAdaptiveRaysChart(raysConfig, standardConfig)

	standardPath := fmt.Sprintf("%s/comparison_standard.png", outputDir)
	err := standardChart.Generate(lowScoreData, "Standard Scaling (No Zoom)", standardPath)
	if err != nil {
		log.Printf("Error generating standard chart: %v", err)
	} else {
		fmt.Printf("   ✓ Standard scaling: %s\n", standardPath)
	}

	// Adaptive chart
	adaptiveChart := charts.NewAdaptiveRaysChart(raysConfig, adaptiveConfig)
	adaptivePath := fmt.Sprintf("%s/comparison_adaptive.png", outputDir)
	err = adaptiveChart.Generate(lowScoreData, "Adaptive Scaling (Zoomed)", adaptivePath)
	if err != nil {
		log.Printf("Error generating adaptive chart: %v", err)
	} else {
		fmt.Printf("   ✓ Adaptive scaling: %s\n", adaptivePath)
	}

	fmt.Println("\n✅ Adaptive Scaling Demo Complete!")
	fmt.Printf("📁 All charts saved to: %s\n", outputDir)

	fmt.Println("\n🎯 **Adaptive Scaling Benefits:**")
	fmt.Println("  • **Visual Impact**: Low scores get proper visual representation")
	fmt.Println("  • **Data Accuracy**: Red gradient shows unused range clearly")
	fmt.Println("  • **Context Preservation**: Labels show actual vs. possible scores")
	fmt.Println("  • **Automatic**: No manual configuration needed")
	fmt.Println("  • **Intelligent**: Only zooms when beneficial")

	fmt.Println("\n🔍 **How It Works:**")
	fmt.Println("  1. **Analyze Data**: Find maximum score in dataset")
	fmt.Println("  2. **Decide Zoom**: If max < threshold (2.5), apply zoom")
	fmt.Println("  3. **Calculate Range**: Zoom to 1.4x max score (min 2.0)")
	fmt.Println("  4. **Visual Zones**: Used range (normal) + unused range (red)")
	fmt.Println("  5. **Smart Labels**: Show actual score vs. full scale")

	fmt.Println("\n🎨 **Visual Elements:**")
	fmt.Println("  • **Zoomed Grid**: Clear grid lines for actual range")
	fmt.Println("  • **Faint Grid**: Original scale shown lightly")
	fmt.Println("  • **Red Gradient**: Unused range clearly marked")
	fmt.Println("  • **Color Coding**: Labels colored by performance level")
	fmt.Println("  • **Zoom Indicator**: Shows zoom level and range")

	fmt.Println("\n📊 **Example Results:**")
	fmt.Println("  • **Max 1.4**: Zooms to 0-2.0, shows 2.0-5.0 in red")
	fmt.Println("  • **Max 2.1**: Zooms to 0-2.9, shows 2.9-5.0 in red")
	fmt.Println("  • **Max 4.2**: No zoom, uses full 0-5.0 scale")

	fmt.Println("\n🚀 **Integration Ready:**")
	fmt.Println("  • Drop-in replacement for existing charts")
	fmt.Println("  • Configurable thresholds and zoom factors")
	fmt.Println("  • Works with all chart types (rays, spider, etc.)")
	fmt.Println("  • Maintains backward compatibility")
}
