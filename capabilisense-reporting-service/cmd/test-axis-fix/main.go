package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🎯 Axis System Fix Test")
	fmt.Println("Testing fix for dual axis system confusion:")
	fmt.Println("  1. Remove overlapping original axis (3, 4)")
	fmt.Println("  2. Show only relevant zoomed axis (0.5, 1.0, 1.5)")
	fmt.Println("  3. Clear, single axis system")

	// Create test data that triggers adaptive scaling
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills & Culture", Score: 1.1},
	}

	// Create generator
	generator := charts.NewUnifiedChartGenerator("./")

	fmt.Println("\n📊 Generating axis-fixed chart...")

	// Generate with single axis system
	config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	config = charts.SwitchToAdaptiveTexturedRays(config)
	
	axisPath, err := generator.GenerateChart(
		testDomains, 5, 
		"", // No title
		"axis_fix_test.png", 
		config)
	if err != nil {
		log.Printf("Error generating axis-fixed chart: %v", err)
	} else {
		fmt.Printf("   ✅ Axis fix: %s\n", axisPath)
	}

	fmt.Println("\n✅ Axis System Fix Applied!")
	fmt.Println("\n🎯 **Axis Confusion Fixed:**")
	fmt.Println("  ✅ **Single Axis System**: Only zoomed axis (0.5, 1.0, 1.5) shown")
	fmt.Println("  ✅ **No Original Axis**: Removed confusing original scale (3, 4)")
	fmt.Println("  ✅ **Clear Positioning**: Axis values at top (12 o'clock)")
	fmt.Println("  ✅ **No Overlap**: Single, coherent axis system")
	fmt.Println("  ✅ **Relevant Scale**: Shows only the scale that matters for the data")
	
	fmt.Println("\n📐 **Technical Changes:**")
	fmt.Println("  • Disabled original grid levels display")
	fmt.Println("  • Show only zoomed/adaptive grid levels")
	fmt.Println("  • Position axis labels at top (centerX-10, centerY-radius-10)")
	fmt.Println("  • Large, clear vector fonts (FontSizeLarge)")
	fmt.Println("  • Single coherent axis system")
	
	fmt.Printf("\n🎯 **Check the result:**\n")
	fmt.Printf("   📁 Axis-fixed chart: %s\n", axisPath)
	fmt.Println("\n💡 **Expected Results:**")
	fmt.Println("   • Only one axis system visible (0.5, 1.0, 1.5)")
	fmt.Println("   • No confusing overlapping numbers")
	fmt.Println("   • Clear, readable axis values")
	fmt.Println("   • Professional, unambiguous scale")
	fmt.Println("   • Axis values positioned at top of circles")
}
