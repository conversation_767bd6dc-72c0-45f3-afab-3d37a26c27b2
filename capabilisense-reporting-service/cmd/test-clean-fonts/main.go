package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🔤 Clean Vector Font Test")
	fmt.Println("Testing vector fonts without outlines/shadows for clean antialiasing...")

	// Create test data with challenging text
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills & Culture", Score: 1.1},
	}

	// Create generator
	generator := charts.NewUnifiedChartGenerator("./")

	fmt.Println("\n📊 Generating clean font charts...")

	// Generate with clean vector fonts (no outlines/shadows)
	fmt.Println("1. Clean Vector Fonts (No Outlines/Shadows)...")
	cleanConfig := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	cleanConfig = charts.SwitchToAdaptiveTexturedRays(cleanConfig)
	
	cleanPath, err := generator.GenerateChart(
		testDomains, 5, 
		"Clean Vector Fonts - No Outlines", 
		"clean_vector_fonts.png", 
		cleanConfig)
	if err != nil {
		log.Printf("Error generating clean font chart: %v", err)
	} else {
		fmt.Printf("   ✅ Clean fonts: %s\n", cleanPath)
	}

	fmt.Println("\n✅ Clean Font Test Complete!")
	fmt.Println("\n🔤 **Clean Font Improvements:**")
	fmt.Println("  ✅ **No Outlines**: Removed problematic outline rendering")
	fmt.Println("  ✅ **No Shadows**: Removed shadow effects that cause artifacts")
	fmt.Println("  ✅ **Better Antialiasing**: HintingNone for smoother edges")
	fmt.Println("  ✅ **Clean Rendering**: Pure vector font rendering")
	fmt.Println("  ✅ **Professional Look**: Clean, modern appearance")
	
	fmt.Println("\n📊 **Antialiasing Improvements:**")
	fmt.Println("  • Changed from HintingFull to HintingNone")
	fmt.Println("  • Removed outline rendering that caused artifacts")
	fmt.Println("  • Removed shadow effects that interfered with antialiasing")
	fmt.Println("  • Pure vector font rendering for smooth edges")
	fmt.Println("  • Better text clarity and readability")
	
	fmt.Printf("\n🎯 **Check the generated file:**\n")
	fmt.Printf("   📁 Clean vector fonts: %s\n", cleanPath)
	fmt.Println("\n💡 **Expected Results:**")
	fmt.Println("   • Large, readable text without pixelation")
	fmt.Println("   • Smooth antialiased edges")
	fmt.Println("   • No weird outline artifacts")
	fmt.Println("   • Clean, professional appearance")
	fmt.Println("   • Proper special character rendering")
}
