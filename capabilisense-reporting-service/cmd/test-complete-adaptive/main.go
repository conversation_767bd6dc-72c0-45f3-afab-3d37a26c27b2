package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	// Create output directory
	outputDir := "./complete-adaptive-demo"
	if err := os.<PERSON>dir<PERSON>ll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	fmt.Println("🎯 Complete Adaptive Scaling Solution")
	fmt.Println("Solving visual consistency across all score ranges...")

	// Create unified chart generator
	generator := charts.NewUnifiedChartGenerator(outputDir)

	// Test scenarios demonstrating the problem and solution
	scenarios := []struct {
		name        string
		description string
		domains     []charts.DomainData
		expectZoom  bool
	}{
		{
			name:        "The Problem: Very Low Scores",
			description: "Without adaptive scaling: tiny sticks on white background",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 1.2},
				{Name: "Operations", Score: 0.8},
				{Name: "Technology", Score: 1.4},
				{Name: "People", Score: 0.9},
				{Name: "Governance", Score: 1.1},
			},
			expectZoom: true,
		},
		{
			name:        "The Solution: Same Data, Adaptive View",
			description: "With adaptive scaling: proper visual impact + red gradient for context",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 1.2},
				{Name: "Operations", Score: 0.8},
				{Name: "Technology", Score: 1.4},
				{Name: "People", Score: 0.9},
				{Name: "Governance", Score: 1.1},
			},
			expectZoom: true,
		},
		{
			name:        "High Scores: No Zoom Needed",
			description: "High scores use full scale naturally",
			domains: []charts.DomainData{
				{Name: "Strategy", Score: 4.2},
				{Name: "Operations", Score: 3.8},
				{Name: "Technology", Score: 4.5},
				{Name: "People", Score: 3.9},
				{Name: "Governance", Score: 4.1},
			},
			expectZoom: false,
		},
	}

	fmt.Println("\n📊 Generating comparison charts...")

	for i, scenario := range scenarios {
		fmt.Printf("\n%d. %s\n", i+1, scenario.name)
		fmt.Printf("   %s\n", scenario.description)

		// Standard chart (no adaptive scaling)
		if i == 0 {
			standardConfig := charts.CreateUnifiedModernConfig(charts.StyleFancyRays)
			standardConfig.AdaptiveConfig = &charts.AdaptiveScalingConfig{
				UseAdaptiveScaling: false, // Disable adaptive scaling
			}

			standardPath, err := generator.GenerateChart(
				scenario.domains, 5, scenario.name + " (Standard)",
				fmt.Sprintf("01_problem_standard.png"), standardConfig)
			if err != nil {
				log.Printf("   ✗ Error: %v", err)
			} else {
				fmt.Printf("   ✓ Standard chart: %s\n", standardPath)
				fmt.Printf("   📊 Result: Tiny rays, poor visual impact\n")
			}
		}

		// Adaptive chart
		if i == 1 {
			adaptiveConfig := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveRays)
			adaptiveConfig = charts.SwitchToAdaptiveRays(adaptiveConfig)

			adaptivePath, err := generator.GenerateChart(
				scenario.domains, 5, scenario.name,
				fmt.Sprintf("02_solution_adaptive.png"), adaptiveConfig)
			if err != nil {
				log.Printf("   ✗ Error: %v", err)
			} else {
				fmt.Printf("   ✓ Adaptive chart: %s\n", adaptivePath)
				fmt.Printf("   📊 Result: Proper visual impact + red gradient context\n")
			}
		}

		// High scores example
		if i == 2 {
			highConfig := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveRays)
			highConfig = charts.SwitchToAdaptiveRays(highConfig)

			highPath, err := generator.GenerateChart(
				scenario.domains, 5, scenario.name,
				fmt.Sprintf("03_high_scores_adaptive.png"), highConfig)
			if err != nil {
				log.Printf("   ✗ Error: %v", err)
			} else {
				fmt.Printf("   ✓ High scores chart: %s\n", highPath)
				fmt.Printf("   📊 Result: Full scale used, no zoom needed\n")
			}
		}
	}

	// Demonstrate different chart styles with adaptive scaling
	fmt.Println("\n🎨 Testing adaptive scaling across different chart styles...")

	lowScoreData := scenarios[0].domains
	chartStyles := []struct {
		style charts.ChartStyle
		name  string
	}{
		{charts.StyleAdaptiveRays, "Adaptive Rays"},
		{charts.StyleFancyRays, "Fancy Rays (with adaptive)"},
		{charts.StyleSVGRays, "SVG Rays (with adaptive)"},
		{charts.StyleSimpleRays, "Simple Rays (with adaptive)"},
	}

	for i, style := range chartStyles {
		config := charts.CreateUnifiedModernConfig(style.style)
		
		// Add adaptive scaling to all styles
		adaptiveConfig := charts.DefaultAdaptiveScalingConfig()
		config.AdaptiveConfig = &adaptiveConfig

		filename := fmt.Sprintf("04_style_%d_%s.png", i+1, string(style.style))
		if style.style == charts.StyleSVGRays {
			filename = fmt.Sprintf("04_style_%d_%s.svg", i+1, string(style.style))
		}

		path, err := generator.GenerateChart(
			lowScoreData, 5, fmt.Sprintf("Low Scores - %s", style.name),
			filename, config)
		if err != nil {
			log.Printf("   ✗ %s: %v", style.name, err)
		} else {
			fmt.Printf("   ✓ %s: %s\n", style.name, path)
		}
	}

	// Demonstrate different zoom levels
	fmt.Println("\n🔍 Testing different zoom levels...")

	zoomTests := []struct {
		name     string
		maxScore float64
		domains  []charts.DomainData
	}{
		{
			name:     "Extreme Low (max 0.7)",
			maxScore: 0.7,
			domains: []charts.DomainData{
				{Name: "A", Score: 0.5}, {Name: "B", Score: 0.3}, {Name: "C", Score: 0.7},
			},
		},
		{
			name:     "Very Low (max 1.4)",
			maxScore: 1.4,
			domains: []charts.DomainData{
				{Name: "A", Score: 1.2}, {Name: "B", Score: 0.8}, {Name: "C", Score: 1.4},
			},
		},
		{
			name:     "Low-Medium (max 2.3)",
			maxScore: 2.3,
			domains: []charts.DomainData{
				{Name: "A", Score: 2.1}, {Name: "B", Score: 1.8}, {Name: "C", Score: 2.3},
			},
		},
		{
			name:     "No Zoom (max 3.5)",
			maxScore: 3.5,
			domains: []charts.DomainData{
				{Name: "A", Score: 3.2}, {Name: "B", Score: 2.8}, {Name: "C", Score: 3.5},
			},
		},
	}

	for i, test := range zoomTests {
		config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveRays)
		config = charts.SwitchToAdaptiveRays(config)

		filename := fmt.Sprintf("05_zoom_%d_max_%.1f.png", i+1, test.maxScore)
		path, err := generator.GenerateChart(
			test.domains, 5, test.name,
			filename, config)
		if err != nil {
			log.Printf("   ✗ %s: %v", test.name, err)
		} else {
			fmt.Printf("   ✓ %s: %s\n", test.name, path)
			
			// Calculate and show zoom info
			chartData := charts.SpiderChartData{Domains: test.domains, MaxLevel: 5}
			scaling := charts.CalculateAdaptiveScaling(chartData, *config.AdaptiveConfig)
			if scaling.IsZoomed {
				fmt.Printf("      🔍 Zoomed to 0-%.1f (red gradient: %.1f-5.0)\n", 
					scaling.ZoomedRange, scaling.ZoomedRange)
			} else {
				fmt.Printf("      📏 Full scale (0-5.0)\n")
			}
		}
	}

	fmt.Println("\n✅ Complete Adaptive Scaling Demo Finished!")
	fmt.Printf("📁 All charts saved to: %s\n", outputDir)

	fmt.Println("\n🎯 **Problem Solved:**")
	fmt.Println("  ❌ **Before**: Low scores = tiny sticks on white background")
	fmt.Println("  ✅ **After**: Low scores = proper visual impact + context preservation")

	fmt.Println("\n🔧 **How Adaptive Scaling Works:**")
	fmt.Println("  1. **Analyze**: Find maximum score in dataset")
	fmt.Println("  2. **Decide**: If max < 2.5, apply intelligent zoom")
	fmt.Println("  3. **Zoom**: Scale to 1.4x max score (minimum 2.0)")
	fmt.Println("  4. **Visualize**: Used range (normal) + unused range (red gradient)")
	fmt.Println("  5. **Inform**: Labels show actual vs. full scale context")

	fmt.Println("\n🎨 **Visual Elements:**")
	fmt.Println("  • **Zoomed Grid**: Clear, readable grid for actual data range")
	fmt.Println("  • **Red Gradient**: Unused range clearly marked as 'problem area'")
	fmt.Println("  • **Smart Labels**: Show both actual score and full scale context")
	fmt.Println("  • **Color Coding**: Performance-based label colors")
	fmt.Println("  • **Zoom Indicator**: Visual and text indicators of zoom level")

	fmt.Println("\n📊 **Benefits:**")
	fmt.Println("  • **Visual Consistency**: Charts look good regardless of score range")
	fmt.Println("  • **Data Accuracy**: Red gradient preserves understanding of full scale")
	fmt.Println("  • **Automatic**: No manual configuration required")
	fmt.Println("  • **Contextual**: Users understand both performance and potential")
	fmt.Println("  • **Professional**: Enterprise-ready visualization quality")

	fmt.Println("\n🚀 **Ready for Production:**")
	fmt.Println("  • Plug-in replacement for existing charts")
	fmt.Println("  • Works with all chart styles (rays, spider, etc.)")
	fmt.Println("  • Configurable thresholds and zoom factors")
	fmt.Println("  • Backward compatible with existing code")
}
