package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🎯 Diagonal Axis Positioning Test")
	fmt.Println("Testing improved axis positioning:")
	fmt.Println("  1. Single axis system (no dual axis confusion)")
	fmt.Println("  2. Diagonal positioning to avoid ray collisions")
	fmt.Println("  3. Clear, readable axis values")

	// Create test data that triggers adaptive scaling
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills & Culture", Score: 1.1},
	}

	// Create generator
	generator := charts.NewUnifiedChartGenerator("./")

	fmt.Println("\n📊 Generating diagonal axis chart...")

	// Generate with diagonal axis positioning
	config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	config = charts.SwitchToAdaptiveTexturedRays(config)
	
	diagonalPath, err := generator.GenerateChart(
		testDomains, 5, 
		"", // No title
		"diagonal_axis_test.png", 
		config)
	if err != nil {
		log.Printf("Error generating diagonal axis chart: %v", err)
	} else {
		fmt.Printf("   ✅ Diagonal axis: %s\n", diagonalPath)
	}

	fmt.Println("\n✅ Diagonal Axis Positioning Applied!")
	fmt.Println("\n🎯 **Axis Positioning Fixed:**")
	fmt.Println("  ✅ **Diagonal Position**: Axis values at 1:30 o'clock (upper-right)")
	fmt.Println("  ✅ **Avoid Ray Collisions**: Positioned away from ray paths")
	fmt.Println("  ✅ **Single Axis System**: Only relevant scale shown")
	fmt.Println("  ✅ **Clear Separation**: No overlap with rays or labels")
	fmt.Println("  ✅ **Large Fonts**: FontSizeLarge (24pt) for readability")
	
	fmt.Println("\n📐 **Technical Implementation:**")
	fmt.Println("  • Position: (centerX + radius*0.5, centerY - radius*0.8)")
	fmt.Println("  • Angle: Upper-right diagonal (~1:30 o'clock)")
	fmt.Println("  • Single axis: Only zoomed/adaptive grid levels")
	fmt.Println("  • No original axis: Removed confusing dual system")
	fmt.Println("  • Vector fonts: Clean, scalable text rendering")
	
	fmt.Printf("\n🎯 **Check the result:**\n")
	fmt.Printf("   📁 Diagonal axis chart: %s\n", diagonalPath)
	fmt.Println("\n💡 **Expected Results:**")
	fmt.Println("   • Axis values positioned diagonally (upper-right)")
	fmt.Println("   • No collision with rays")
	fmt.Println("   • Single, clear axis system (0.5, 1.0, 1.5)")
	fmt.Println("   • Large, readable vector fonts")
	fmt.Println("   • Professional, unambiguous layout")
}
