package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	// Create output directory
	outputDir := "./fancy-charts"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	// Create chart generator
	generator := charts.NewChartGenerator(outputDir)

	// Example domain data (StockSavvy style)
	domains := []charts.DomainData{
		{Name: "Return on Equity", Score: 4.2},
		{Name: "3M Volatility", Score: 2.8},
		{Name: "Dividend Yield", Score: 3.5},
		{Name: "5Y Dividend Growth", Score: 4.0},
		{Name: "P/E Ratio", Score: 3.2},
		{Name: "P/CF Ratio", Score: 2.9},
		{Name: "5Y EPS Growth", Score: 3.8},
		{Name: "Return on Assets", Score: 3.6},
		{Name: "Net Profit Margin", Score: 4.1},
		{Name: "Debt/Equity", Score: 2.5},
	}

	fmt.Println("🎨 Generating Fancy Rays Charts...")
	fmt.Println("Comparing different visualization strategies:")

	// 1. Current simple rays (for comparison)
	fmt.Println("\n1. Current Simple Rays (baseline)...")
	simpleConfig := charts.CreateModernRaysConfig()
	simplePath, err := generator.GenerateCustomRaysChart(
		domains,
		5,
		"Simple Rays - Current Implementation",
		"01_simple_rays.png",
		&simpleConfig,
	)
	if err != nil {
		log.Printf("Error generating simple rays: %v", err)
	} else {
		fmt.Printf("   ✓ Simple rays: %s\n", simplePath)
	}

	// 2. Enhanced raster graphics
	fmt.Println("\n2. Enhanced Raster Graphics (anti-aliased, effects)...")
	fancyChart := charts.NewFancyRaysChart(simpleConfig)
	fancyPath := outputDir + "/02_fancy_raster_rays.png"
	err = fancyChart.Generate(charts.SpiderChartData{
		Domains:    domains,
		MaxLevel:   5,
		ChartTitle: "Fancy Raster Rays - Enhanced Graphics",
	}, "Fancy Raster Rays - Enhanced Graphics", fancyPath)
	if err != nil {
		log.Printf("Error generating fancy raster rays: %v", err)
	} else {
		fmt.Printf("   ✓ Fancy raster rays: %s\n", fancyPath)
	}

	// 3. Textured rays
	fmt.Println("\n3. Textured Rays (procedural textures)...")
	texturedChart := charts.NewTexturedRaysChart(simpleConfig)
	texturedPath := outputDir + "/03_textured_rays.png"
	err = texturedChart.Generate(charts.SpiderChartData{
		Domains:    domains,
		MaxLevel:   5,
		ChartTitle: "Textured Rays - Procedural Patterns",
	}, "Textured Rays - Procedural Patterns", texturedPath)
	if err != nil {
		log.Printf("Error generating textured rays: %v", err)
	} else {
		fmt.Printf("   ✓ Textured rays: %s\n", texturedPath)
	}

	// 4. SVG rays (most sophisticated)
	fmt.Println("\n4. SVG Rays (vector graphics, scalable)...")
	svgChart := charts.NewSVGRaysChart(simpleConfig)
	svgPath := outputDir + "/04_svg_rays.svg"
	err = svgChart.Generate(charts.SpiderChartData{
		Domains:    domains,
		MaxLevel:   5,
		ChartTitle: "SVG Rays - Vector Graphics",
	}, "SVG Rays - Vector Graphics", svgPath)
	if err != nil {
		log.Printf("Error generating SVG rays: %v", err)
	} else {
		fmt.Printf("   ✓ SVG rays: %s\n", svgPath)
	}

	// 5. Comparison with different data sets
	fmt.Println("\n5. Testing with different data patterns...")
	
	// High scores (all above 4.0)
	highScores := []charts.DomainData{
		{Name: "Excellence A", Score: 4.8},
		{Name: "Excellence B", Score: 4.5},
		{Name: "Excellence C", Score: 4.2},
		{Name: "Excellence D", Score: 4.7},
		{Name: "Excellence E", Score: 4.3},
	}
	
	highConfig := charts.CreateModernRaysConfig()
	highPath, err := generator.GenerateCustomRaysChart(
		highScores,
		5,
		"High Performance Pattern",
		"05_high_scores_pattern.png",
		&highConfig,
	)
	if err != nil {
		log.Printf("Error generating high scores pattern: %v", err)
	} else {
		fmt.Printf("   ✓ High scores pattern: %s\n", highPath)
	}

	// Low scores (all below 2.5)
	lowScores := []charts.DomainData{
		{Name: "Challenge A", Score: 1.8},
		{Name: "Challenge B", Score: 2.1},
		{Name: "Challenge C", Score: 1.5},
		{Name: "Challenge D", Score: 2.3},
		{Name: "Challenge E", Score: 1.9},
	}
	
	lowPath, err := generator.GenerateCustomRaysChart(
		lowScores,
		5,
		"Improvement Opportunities Pattern",
		"06_low_scores_pattern.png",
		&highConfig,
	)
	if err != nil {
		log.Printf("Error generating low scores pattern: %v", err)
	} else {
		fmt.Printf("   ✓ Low scores pattern: %s\n", lowPath)
	}

	// Mixed scores (realistic business scenario)
	mixedScores := []charts.DomainData{
		{Name: "Strategy", Score: 4.2},
		{Name: "Leadership", Score: 3.8},
		{Name: "Innovation", Score: 2.1},
		{Name: "Operations", Score: 3.5},
		{Name: "Technology", Score: 1.8},
		{Name: "Culture", Score: 4.0},
		{Name: "Processes", Score: 2.7},
		{Name: "Analytics", Score: 1.5},
	}
	
	mixedPath, err := generator.GenerateCustomRaysChart(
		mixedScores,
		5,
		"Realistic Business Assessment",
		"07_mixed_scores_realistic.png",
		&highConfig,
	)
	if err != nil {
		log.Printf("Error generating mixed scores pattern: %v", err)
	} else {
		fmt.Printf("   ✓ Mixed scores pattern: %s\n", mixedPath)
	}

	fmt.Println("\n✅ All fancy rays charts generated successfully!")
	fmt.Printf("📁 Charts saved to: %s\n", outputDir)
	
	fmt.Println("\n📊 **Comparison Summary:**")
	fmt.Println("  1. **Simple Rays**: Basic colored lines (current)")
	fmt.Println("  2. **Fancy Raster**: Anti-aliased with gradients and effects")
	fmt.Println("  3. **Textured Rays**: Procedural scale patterns")
	fmt.Println("  4. **SVG Rays**: Vector graphics with professional effects")
	
	fmt.Println("\n🎯 **Recommendations:**")
	fmt.Println("  • **For Web/PDF Reports**: Use SVG rays (scalable, professional)")
	fmt.Println("  • **For High-Quality Prints**: Use fancy raster rays")
	fmt.Println("  • **For Unique Branding**: Use textured rays")
	fmt.Println("  • **For Performance**: Stick with simple rays")
	
	fmt.Println("\n💡 **Next Steps:**")
	fmt.Println("  1. Review generated charts to compare visual quality")
	fmt.Println("  2. Choose preferred approach based on requirements")
	fmt.Println("  3. Integrate chosen approach into main chart generator")
	fmt.Println("  4. Add configuration options for different visual styles")
	
	fmt.Println("\n🔍 **Visual Features to Look For:**")
	fmt.Println("  • Expanding ray segments (like StockSavvy)")
	fmt.Println("  • Smooth gradients and color transitions")
	fmt.Println("  • Professional shadows and glow effects")
	fmt.Println("  • Clear visual separation between domains")
	fmt.Println("  • Scalable quality across different sizes")
}
