package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🎯 Final Fixes Test")
	fmt.Println("Testing specific fixes:")
	fmt.Println("  1. Side labels don't overlay rays")
	fmt.Println("  2. Larger axis values")
	fmt.Println("  3. No top header/subheader")

	// Create test data
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills & Culture", Score: 1.1},
	}

	// Create generator
	generator := charts.NewUnifiedChartGenerator("./")

	fmt.Println("\n📊 Generating final fixed chart...")

	// Generate with all specific fixes
	config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	config = charts.SwitchToAdaptiveTexturedRays(config)
	
	finalPath, err := generator.GenerateChart(
		testDomains, 5, 
		"This Title Should Not Appear", // This should be ignored
		"final_fixes_test.png", 
		config)
	if err != nil {
		log.Printf("Error generating final fixed chart: %v", err)
	} else {
		fmt.Printf("   ✅ Final fixes: %s\n", finalPath)
	}

	fmt.Println("\n✅ Final Fixes Applied!")
	fmt.Println("\n🎯 **Specific Issues Fixed:**")
	fmt.Println("  ✅ **No Ray Overlay**: Labels positioned +100px from rays with +20px extra spacing")
	fmt.Println("  ✅ **Larger Axis Values**: Changed from FontSizeSmall to FontSizeLarge")
	fmt.Println("  ✅ **No Headers**: Title and zoom indicator completely removed")
	fmt.Println("  ✅ **Better Positioning**: Improved angle-based label placement")
	fmt.Println("  ✅ **Clean Layout**: No title space needed, more room for chart")
	
	fmt.Println("\n📐 **Technical Changes:**")
	fmt.Println("  • Label radius: MaxRadius + 100px (was +60px)")
	fmt.Println("  • Extra spacing: +20px to avoid ray overlay")
	fmt.Println("  • Axis font size: FontSizeLarge (24pt)")
	fmt.Println("  • Title drawing: Completely disabled")
	fmt.Println("  • Zoom indicator: Completely disabled")
	fmt.Println("  • Top margin: Reduced to 10px (was 60px)")
	
	fmt.Printf("\n🎯 **Check the result:**\n")
	fmt.Printf("   📁 Final chart: %s\n", finalPath)
	fmt.Println("\n💡 **Expected Results:**")
	fmt.Println("   • No title or subtitle at top")
	fmt.Println("   • Large, readable axis values")
	fmt.Println("   • Labels positioned clear of rays")
	fmt.Println("   • Clean, professional appearance")
	fmt.Println("   • Maximum chart area utilization")
}
