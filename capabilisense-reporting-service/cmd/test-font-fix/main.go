package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🔤 Testing REAL Font Improvements")
	fmt.Println("Generating chart with improved fonts...")

	// Create test data with special characters
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills", Score: 1.1},
	}

	// Create unified config for adaptive textured rays
	config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	config = charts.SwitchToAdaptiveTexturedRays(config)

	// Generate chart using the correct API
	generator := charts.NewUnifiedChartGenerator("./")
	outputPath, err := generator.GenerateChart(testDomains, 5, "Font Test - Large Readable Text", "font_test_improved.png", config)
	if err != nil {
		log.Fatalf("Failed to generate chart: %v", err)
	}

	fmt.Printf("✅ Chart generated: %s\n", outputPath)
	fmt.Println("📊 Check the generated chart to see if fonts are now large and readable!")
}
