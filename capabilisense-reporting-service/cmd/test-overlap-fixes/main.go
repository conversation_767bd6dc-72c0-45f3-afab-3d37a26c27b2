package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🎯 Overlap & Cutoff Fixes Test")
	fmt.Println("Testing specific fixes:")
	fmt.Println("  1. Top label not cut off")
	fmt.Println("  2. Axis values don't overlap")
	fmt.Println("  3. Clean positioning")

	// Create test data
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills & Culture", Score: 1.1},
	}

	// Create generator
	generator := charts.NewUnifiedChartGenerator("./")

	fmt.Println("\n📊 Generating overlap-fixed chart...")

	// Generate with overlap fixes
	config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	config = charts.SwitchToAdaptiveTexturedRays(config)
	
	overlapPath, err := generator.GenerateChart(
		testDomains, 5, 
		"", // No title
		"overlap_fixes_test.png", 
		config)
	if err != nil {
		log.Printf("Error generating overlap-fixed chart: %v", err)
	} else {
		fmt.Printf("   ✅ Overlap fixes: %s\n", overlapPath)
	}

	fmt.Println("\n✅ Overlap & Cutoff Fixes Applied!")
	fmt.Println("\n🎯 **Latest Issues Fixed:**")
	fmt.Println("  ✅ **Top Label Safe**: Increased top margin from 10px to 40px")
	fmt.Println("  ✅ **Axis Values Separated**: Diagonal positioning at 0.7 radius")
	fmt.Println("  ✅ **No Overlap**: Grid labels positioned at top-right diagonal")
	fmt.Println("  ✅ **Clean Spacing**: Proper separation between all text elements")
	
	fmt.Println("\n📐 **Technical Changes:**")
	fmt.Println("  • Top margin: Increased to 40px to prevent label cutoff")
	fmt.Println("  • Axis positioning: Diagonal at (centerX + radius*0.7, centerY - radius*0.7)")
	fmt.Println("  • Faint labels: FontSizeMedium for better hierarchy")
	fmt.Println("  • Prominent labels: FontSizeLarge for main values")
	fmt.Println("  • All labels: Clean vector fonts without outlines")
	
	fmt.Printf("\n🎯 **Check the result:**\n")
	fmt.Printf("   📁 Overlap-fixed chart: %s\n", overlapPath)
	fmt.Println("\n💡 **Expected Results:**")
	fmt.Println("   • Top label fully visible within image")
	fmt.Println("   • Axis values clearly separated")
	fmt.Println("   • No text overlapping")
	fmt.Println("   • Professional, clean layout")
	fmt.Println("   • Large, readable fonts throughout")
}
