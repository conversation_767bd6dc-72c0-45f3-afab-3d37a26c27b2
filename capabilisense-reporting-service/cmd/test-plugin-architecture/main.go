package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	// Create output directory
	outputDir := "./plugin-demo"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	// Create unified chart generator
	generator := charts.NewUnifiedChartGenerator(outputDir)

	// Sample data
	domains := []charts.DomainData{
		{Name: "Strategy", Score: 4.2},
		{Name: "Operations", Score: 3.1},
		{Name: "Technology", Score: 2.8},
		{Name: "People", Score: 3.7},
		{Name: "Governance", Score: 2.5},
		{Name: "Innovation", Score: 3.9},
	}

	fmt.Println("🔌 Demonstrating Plug-in Chart Architecture")
	fmt.Println("Same data, same config, different renderers...")

	// Base configuration
	baseConfig := charts.CreateUnifiedProfessionalConfig(charts.StyleSimpleRays)

	// 1. Simple Rays (current implementation)
	fmt.Println("\n1. Switching to Simple Rays...")
	simpleConfig := baseConfig
	simpleConfig.Style = charts.StyleSimpleRays
	
	path1, err := generator.GenerateChart(
		domains, 5, "Professional Assessment - Simple Rays",
		"01_simple_rays_plugin.png", simpleConfig)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("   ✓ Generated: %s\n", path1)
	}

	// 2. Fancy Rays (enhanced graphics)
	fmt.Println("\n2. Switching to Fancy Rays...")
	fancyConfig := charts.SwitchToFancyRays(baseConfig)
	
	path2, err := generator.GenerateChart(
		domains, 5, "Professional Assessment - Fancy Rays",
		"02_fancy_rays_plugin.png", fancyConfig)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("   ✓ Generated: %s\n", path2)
	}

	// 3. SVG Rays (vector graphics)
	fmt.Println("\n3. Switching to SVG Rays...")
	svgConfig := charts.SwitchToSVGRays(baseConfig)
	
	path3, err := generator.GenerateChart(
		domains, 5, "Professional Assessment - SVG Rays",
		"03_svg_rays_plugin.svg", svgConfig)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("   ✓ Generated: %s\n", path3)
	}

	// 4. Textured Rays (procedural textures)
	fmt.Println("\n4. Switching to Textured Rays...")
	texturedConfig := charts.SwitchToTexturedRays(baseConfig)
	
	path4, err := generator.GenerateChart(
		domains, 5, "Professional Assessment - Textured Rays",
		"04_textured_rays_plugin.png", texturedConfig)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("   ✓ Generated: %s\n", path4)
	}

	// 5. Spider Chart (traditional)
	fmt.Println("\n5. Switching to Spider Chart...")
	spiderConfig := baseConfig
	spiderConfig.Style = charts.StyleSpider
	
	path5, err := generator.GenerateChart(
		domains, 5, "Professional Assessment - Spider Chart",
		"05_spider_plugin.png", spiderConfig)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("   ✓ Generated: %s\n", path5)
	}

	// 6. Demonstrate runtime switching
	fmt.Println("\n6. Runtime Style Switching Demo...")
	
	styles := []charts.ChartStyle{
		charts.StyleSimpleRays,
		charts.StyleFancyRays,
		charts.StyleSVGRays,
		charts.StyleTexturedRays,
		charts.StyleSpider,
	}
	
	for i, style := range styles {
		config := charts.CreateUnifiedModernConfig(style)
		filename := fmt.Sprintf("06_runtime_switch_%d_%s.png", i+1, string(style))
		if style == charts.StyleSVGRays {
			filename = fmt.Sprintf("06_runtime_switch_%d_%s.svg", i+1, string(style))
		}
		
		path, err := generator.GenerateChart(
			domains, 5, fmt.Sprintf("Runtime Switch - %s", style),
			filename, config)
		if err != nil {
			log.Printf("Error with %s: %v", style, err)
		} else {
			fmt.Printf("   ✓ %s: %s\n", style, path)
		}
	}

	fmt.Println("\n✅ Plug-in Architecture Demo Complete!")
	fmt.Printf("📁 All charts saved to: %s\n", outputDir)

	fmt.Println("\n🔌 **Architecture Benefits:**")
	fmt.Println("  • **Same Interface**: All renderers implement Generate(data, title, path)")
	fmt.Println("  • **Easy Switching**: Change one config parameter to switch styles")
	fmt.Println("  • **Consistent Data**: Same SpiderChartData works with all renderers")
	fmt.Println("  • **Runtime Selection**: Can switch styles based on user preferences")
	fmt.Println("  • **Backward Compatible**: Existing code continues to work")

	fmt.Println("\n🚀 **Integration Examples:**")
	fmt.Println("  • **PDF Reports**: Switch to SVG for scalable charts")
	fmt.Println("  • **Web Dashboard**: Use fancy rays for interactive displays")
	fmt.Println("  • **Print Materials**: Use textured rays for unique branding")
	fmt.Println("  • **Performance Mode**: Fall back to simple rays for speed")

	fmt.Println("\n⚙️ **Configuration Switching:**")
	fmt.Println("  • charts.SwitchToFancyRays(config)")
	fmt.Println("  • charts.SwitchToSVGRays(config)")
	fmt.Println("  • charts.SwitchToTexturedRays(config)")
	fmt.Println("  • config.Style = charts.StyleSimpleRays")

	fmt.Println("\n🎯 **Next Steps:**")
	fmt.Println("  1. Update ChartGenerator to use UnifiedChartGenerator")
	fmt.Println("  2. Add style selection to API endpoints")
	fmt.Println("  3. Update PDF renderer to use preferred style")
	fmt.Println("  4. Add user preference storage for chart styles")
}
