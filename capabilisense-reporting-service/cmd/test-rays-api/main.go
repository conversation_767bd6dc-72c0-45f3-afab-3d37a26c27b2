package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	// Start chart API server in background
	go startChartServer()

	// Wait for server to start
	time.Sleep(2 * time.Second)

	fmt.Println("🚀 Testing Rays Chart API...")

	// Test 1: Generate rays chart with custom data
	fmt.Println("\n1. Testing rays chart generation with custom data...")
	testRaysChartGeneration()

	// Test 2: Generate spider chart for comparison
	fmt.Println("\n2. Testing spider chart generation for comparison...")
	testSpiderChartGeneration()

	// Test 3: Test different presets
	fmt.Println("\n3. Testing different chart presets...")
	testChartPresets()

	// Test 4: List generated charts
	fmt.Println("\n4. Listing all generated charts...")
	testListCharts()

	fmt.Println("\n✅ All API tests completed!")
	fmt.Println("📁 Charts available at: http://localhost:8080/charts/")
	fmt.Println("🌐 API documentation: http://localhost:8080/api/v1/charts")

	// Keep server running for manual testing
	fmt.Println("\n🔄 Server running at http://localhost:8080")
	fmt.Println("Press Ctrl+C to stop...")
	select {} // Block forever
}

func startChartServer() {
	// Setup chart routes
	mux := charts.SetupChartRoutes("./api-charts", "http://localhost:8080")

	// Add a simple index page
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		html := `
<!DOCTYPE html>
<html>
<head>
    <title>Chart API Test Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .method { color: #007acc; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Chart API Test Server</h1>
    <h2>Available Endpoints:</h2>

    <div class="endpoint">
        <span class="method">POST</span> /api/v1/generate-chart
        <br>Generate spider or rays charts
    </div>

    <div class="endpoint">
        <span class="method">GET</span> /api/v1/charts
        <br>List all generated charts
    </div>

    <div class="endpoint">
        <span class="method">GET</span> /charts/{filename}
        <br>Serve chart images
    </div>

    <h2>Example Request:</h2>
    <pre>{
  "chart_type": "rays",
  "config_preset": "modern",
  "custom_data": {
    "domains": [
      {"name": "Strategy", "score": 3.5},
      {"name": "Operations", "score": 2.8},
      {"name": "Technology", "score": 4.2}
    ],
    "max_level": 5,
    "chart_title": "Assessment Results"
  }
}</pre>
</body>
</html>`
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(html))
	})

	fmt.Println("Starting chart API server on :8080...")
	log.Fatal(http.ListenAndServe(":8080", mux))
}

func testRaysChartGeneration() {
	// Create test data
	requestData := map[string]interface{}{
		"chart_type":    "rays",
		"config_preset": "modern",
		"filename":      "test_rays_chart.png",
		"custom_data": map[string]interface{}{
			"domains": []map[string]interface{}{
				{"name": "Return on Equity", "score": 4.2},
				{"name": "3M Volatility", "score": 2.8},
				{"name": "Dividend Yield", "score": 3.5},
				{"name": "5Y Dividend Growth", "score": 4.0},
				{"name": "P/E", "score": 3.2},
				{"name": "P/CF", "score": 2.9},
				{"name": "5Y EPS Growth", "score": 3.8},
				{"name": "Return on Assets", "score": 3.6},
			},
			"max_level":   5,
			"chart_title": "Stock Analysis - Rays Chart",
		},
	}

	response := makeAPIRequest("POST", "/api/v1/generate-chart", requestData)
	if response["success"].(bool) {
		fmt.Printf("   ✓ Rays chart generated: %s\n", response["chart_url"])
		fmt.Printf("   ✓ Generation time: %.0fms\n", response["metadata"].(map[string]interface{})["generation_time_ms"])
	} else {
		fmt.Printf("   ✗ Failed: %s\n", response["error"])
	}
}

func testSpiderChartGeneration() {
	// Create test data
	requestData := map[string]interface{}{
		"chart_type":    "spider",
		"config_preset": "professional",
		"filename":      "test_spider_chart.png",
		"custom_data": map[string]interface{}{
			"domains": []map[string]interface{}{
				{"name": "Strategy", "score": 3.5},
				{"name": "Operations", "score": 2.8},
				{"name": "Technology", "score": 4.2},
				{"name": "People", "score": 3.1},
				{"name": "Governance", "score": 2.5},
			},
			"max_level":   5,
			"chart_title": "Maturity Assessment - Spider Chart",
		},
	}

	response := makeAPIRequest("POST", "/api/v1/generate-chart", requestData)
	if response["success"].(bool) {
		fmt.Printf("   ✓ Spider chart generated: %s\n", response["chart_url"])
		fmt.Printf("   ✓ Generation time: %.0fms\n", response["metadata"].(map[string]interface{})["generation_time_ms"])
	} else {
		fmt.Printf("   ✗ Failed: %s\n", response["error"])
	}
}

func testChartPresets() {
	presets := []struct {
		chartType string
		preset    string
		filename  string
	}{
		{"rays", "professional", "rays_professional.png"},
		{"rays", "modern", "rays_modern.png"},
		{"spider", "high_contrast", "spider_high_contrast.png"},
		{"spider", "modern_gradient", "spider_modern_gradient.png"},
	}

	domains := []map[string]interface{}{
		{"name": "Domain A", "score": 3.5},
		{"name": "Domain B", "score": 2.8},
		{"name": "Domain C", "score": 4.2},
		{"name": "Domain D", "score": 3.1},
	}

	for _, test := range presets {
		requestData := map[string]interface{}{
			"chart_type":    test.chartType,
			"config_preset": test.preset,
			"filename":      test.filename,
			"custom_data": map[string]interface{}{
				"domains":     domains,
				"max_level":   5,
				"chart_title": fmt.Sprintf("%s Chart - %s Preset", test.chartType, test.preset),
			},
		}

		response := makeAPIRequest("POST", "/api/v1/generate-chart", requestData)
		if response["success"].(bool) {
			fmt.Printf("   ✓ %s chart (%s preset): %s\n", test.chartType, test.preset, response["chart_url"])
		} else {
			fmt.Printf("   ✗ %s chart (%s preset) failed: %s\n", test.chartType, test.preset, response["error"])
		}
	}
}

func testListCharts() {
	response := makeAPIRequest("GET", "/api/v1/charts", nil)
	if charts, ok := response["charts"].([]interface{}); ok {
		fmt.Printf("   ✓ Found %d charts:\n", len(charts))
		for _, chart := range charts {
			chartMap := chart.(map[string]interface{})
			fmt.Printf("     - %s (%s)\n", chartMap["filename"], chartMap["url"])
		}
	} else {
		fmt.Printf("   ✗ Failed to list charts\n")
	}
}

func makeAPIRequest(method, endpoint string, data interface{}) map[string]interface{} {
	url := "http://localhost:8080" + endpoint

	var body io.Reader
	if data != nil {
		jsonData, _ := json.Marshal(data)
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return map[string]interface{}{"success": false, "error": err.Error()}
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return map[string]interface{}{"success": false, "error": err.Error()}
	}
	defer resp.Body.Close()

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return map[string]interface{}{"success": false, "error": err.Error()}
	}

	return response
}
