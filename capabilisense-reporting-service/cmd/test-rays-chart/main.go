package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	// Create output directory
	outputDir := "./test-charts"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	// Create chart generator
	generator := charts.NewChartGenerator(outputDir)

	// Example domain data similar to StockSavvy example
	domains := []charts.DomainData{
		{Name: "Return on Equity", Score: 4.2},
		{Name: "3M Volatility", Score: 2.8},
		{Name: "Dividend Yield", Score: 3.5},
		{Name: "5Y Dividend Growth", Score: 4.0},
		{Name: "P/E", Score: 3.2},
		{Name: "P/CF", Score: 2.9},
		{Name: "5Y EPS Growth", Score: 3.8},
		{Name: "Return on Assets", Score: 3.6},
		{Name: "Net Profit Margin", Score: 4.1},
		{Name: "Debt/Equity", Score: 2.5},
		{Name: "12M EPS Growth", Score: 3.3},
	}

	fmt.Println("Generating rays charts...")

	// Generate professional rays chart
	fmt.Println("1. Generating professional rays chart...")
	professionalConfig := charts.CreateProfessionalRaysConfig()
	professionalPath, err := generator.GenerateCustomRaysChart(
		domains,
		5, // Max level
		"Professional Rays Chart",
		"professional_rays_chart.png",
		&professionalConfig,
	)
	if err != nil {
		log.Printf("Error generating professional rays chart: %v", err)
	} else {
		fmt.Printf("   ✓ Professional rays chart: %s\n", professionalPath)
	}

	// Generate modern rays chart (StockSavvy style)
	fmt.Println("2. Generating modern rays chart (StockSavvy style)...")
	modernConfig := charts.CreateModernRaysConfig()
	modernPath, err := generator.GenerateCustomRaysChart(
		domains,
		5, // Max level
		"Stock Analysis - Relative to S&P500 Companies",
		"modern_rays_chart.png",
		&modernConfig,
	)
	if err != nil {
		log.Printf("Error generating modern rays chart: %v", err)
	} else {
		fmt.Printf("   ✓ Modern rays chart: %s\n", modernPath)
	}

	// Generate compact rays chart for fewer domains
	fmt.Println("3. Generating compact rays chart...")
	compactDomains := []charts.DomainData{
		{Name: "Strategy", Score: 3.5},
		{Name: "Operations", Score: 2.8},
		{Name: "Technology", Score: 4.2},
		{Name: "People", Score: 3.1},
		{Name: "Governance", Score: 2.5},
	}

	compactConfig := charts.DefaultRaysChartConfig()
	compactConfig.Width = 700
	compactConfig.Height = 700
	compactConfig.CenterX = 350
	compactConfig.CenterY = 350
	compactConfig.MaxRadius = 250
	compactConfig.RayWidth = 35.0
	compactConfig.ModernStyle = true
	compactConfig.UseGradientRays = true

	compactPath, err := generator.GenerateCustomRaysChart(
		compactDomains,
		5, // Max level
		"Organizational Maturity Assessment",
		"compact_rays_chart.png",
		&compactConfig,
	)
	if err != nil {
		log.Printf("Error generating compact rays chart: %v", err)
	} else {
		fmt.Printf("   ✓ Compact rays chart: %s\n", compactPath)
	}

	// Generate comparison: spider vs rays
	fmt.Println("4. Generating comparison charts (spider vs rays)...")
	
	// Spider chart for comparison
	spiderConfig := charts.CreateProfessionalConfig()
	spiderPath, err := generator.GenerateCustomSpiderChart(
		compactDomains,
		5, // Max level
		"Spider Chart - Organizational Maturity",
		"comparison_spider_chart.png",
		&spiderConfig,
	)
	if err != nil {
		log.Printf("Error generating comparison spider chart: %v", err)
	} else {
		fmt.Printf("   ✓ Comparison spider chart: %s\n", spiderPath)
	}

	// Rays chart for comparison
	comparisonRaysConfig := charts.CreateProfessionalRaysConfig()
	comparisonRaysPath, err := generator.GenerateCustomRaysChart(
		compactDomains,
		5, // Max level
		"Rays Chart - Organizational Maturity",
		"comparison_rays_chart.png",
		&comparisonRaysConfig,
	)
	if err != nil {
		log.Printf("Error generating comparison rays chart: %v", err)
	} else {
		fmt.Printf("   ✓ Comparison rays chart: %s\n", comparisonRaysPath)
	}

	fmt.Println("\n✅ All charts generated successfully!")
	fmt.Printf("📁 Charts saved to: %s\n", outputDir)
	fmt.Println("\nChart types generated:")
	fmt.Println("  • Professional rays chart - clean, business-ready styling")
	fmt.Println("  • Modern rays chart - colorful, StockSavvy-inspired design")
	fmt.Println("  • Compact rays chart - optimized for fewer domains")
	fmt.Println("  • Comparison charts - spider vs rays side-by-side")
	
	fmt.Println("\n💡 The rays chart offers several advantages over spider charts:")
	fmt.Println("  • Better visual separation of domains")
	fmt.Println("  • Easier to read individual scores")
	fmt.Println("  • More modern, professional appearance")
	fmt.Println("  • Better space utilization")
	fmt.Println("  • Cleaner design for reports and presentations")
}
