package main

import (
	"fmt"
	"log"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	fmt.Println("🔤 Vector Font vs Bitmap Font Comparison")
	fmt.Println("Testing the new vector font system...")

	// Create test data with challenging text
	testDomains := []charts.DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Data/Analytics", Score: 0.8},
		{Name: "Technology-Infrastructure", Score: 1.5},
		{Name: "Process (Optimization)", Score: 0.9},
		{Name: "People: Skills & Culture", Score: 1.1},
	}

	// Create generator
	generator := charts.NewUnifiedChartGenerator("./")

	fmt.Println("\n📊 Generating comparison charts...")

	// 1. Generate with new vector font system (adaptive textured rays)
	fmt.Println("1. Vector Fonts (New System)...")
	vectorConfig := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	vectorConfig = charts.SwitchToAdaptiveTexturedRays(vectorConfig)
	
	vectorPath, err := generator.GenerateChart(
		testDomains, 5, 
		"Vector Fonts - Crisp & Scalable", 
		"vector_fonts_test.png", 
		vectorConfig)
	if err != nil {
		log.Printf("Error generating vector font chart: %v", err)
	} else {
		fmt.Printf("   ✅ Vector fonts: %s\n", vectorPath)
	}

	// 2. Generate with textured rays (which might still use old fonts in some places)
	fmt.Println("2. Comparison Chart...")
	texturedConfig := charts.CreateUnifiedModernConfig(charts.StyleTexturedRays)
	
	texturedPath, err := generator.GenerateChart(
		testDomains, 5, 
		"Comparison - Mixed Font System", 
		"comparison_fonts_test.png", 
		texturedConfig)
	if err != nil {
		log.Printf("Error generating comparison chart: %v", err)
	} else {
		fmt.Printf("   ✅ Comparison: %s\n", texturedPath)
	}

	fmt.Println("\n✅ Font Comparison Test Complete!")
	fmt.Println("\n🔤 **Vector Font Advantages:**")
	fmt.Println("  ✅ **True Scalability**: Crisp at any size")
	fmt.Println("  ✅ **High Quality**: Anti-aliased rendering")
	fmt.Println("  ✅ **Accurate Metrics**: Precise text measurement")
	fmt.Println("  ✅ **Professional Appearance**: Enterprise-ready")
	fmt.Println("  ✅ **No Pixelation**: Smooth curves and edges")
	fmt.Println("  ✅ **Better Readability**: Clear at small and large sizes")
	
	fmt.Println("\n📊 **Technical Improvements:**")
	fmt.Println("  • Uses Go's built-in vector fonts (goregular.TTF, gobold.TTF)")
	fmt.Println("  • OpenType font parsing with full hinting")
	fmt.Println("  • Accurate font metrics for positioning")
	fmt.Println("  • Scalable font sizes in points (14pt, 18pt, 24pt, 32pt, 40pt)")
	fmt.Println("  • No external font file dependencies")
	
	fmt.Printf("\n🎯 **Check the generated files:**\n")
	fmt.Printf("   📁 Vector fonts: %s\n", vectorPath)
	fmt.Printf("   📁 Comparison: %s\n", texturedPath)
}
