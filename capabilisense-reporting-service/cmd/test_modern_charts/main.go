package main

import (
	"log"
	"os"

	"capabilisense-reporting-service/pkg/charts"
)

func main() {
	log.Println("🎨 Testing Modern Professional Chart Styles")

	// Create output directory
	outputDir := "test_modern_charts"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	// Create chart generator
	generator := charts.NewChartGenerator(outputDir)

	// Test data - similar to assessment results
	domains := []charts.DomainData{
		{Name: "Strategy & Vision", Score: 3.8},
		{Name: "Leadership", Score: 2.9},
		{Name: "Technology", Score: 4.2},
		{Name: "Operations", Score: 3.5},
		{Name: "People & Culture", Score: 2.7},
		{Name: "Data & Analytics", Score: 3.9},
		{Name: "Innovation", Score: 3.1},
		{Name: "Governance", Score: 2.8},
	}

	// Test 1: Default Configuration
	log.Println("📊 Generating default chart...")
	testDefaultChart(generator, domains)

	// Test 2: Professional Configuration
	log.Println("💼 Generating professional chart...")
	testProfessionalChart(generator, domains)

	// Test 3: Modern Gradient Configuration (like the example)
	log.Println("🌈 Generating modern gradient chart...")
	testModernGradientChart(generator, domains)

	// Test 4: High Contrast Configuration
	log.Println("🔍 Generating high contrast chart...")
	testHighContrastChart(generator, domains)

	log.Println("✅ All modern chart tests completed successfully!")
	log.Printf("📁 Charts saved in: %s", outputDir)
	log.Println("🎯 Compare the charts to see the visual improvements!")
}

func testDefaultChart(generator *charts.ChartGenerator, domains []charts.DomainData) {
	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"Default Style - Maturity Assessment",
		"default_style_chart.png",
		nil, // Use default config
	)

	if err != nil {
		log.Printf("❌ Error generating default chart: %v", err)
		return
	}

	log.Printf("✅ Default chart generated: %s", outputPath)
}

func testProfessionalChart(generator *charts.ChartGenerator, domains []charts.DomainData) {
	config := charts.CreateProfessionalConfig()
	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"Professional Style - Maturity Assessment",
		"professional_style_chart.png",
		&config,
	)

	if err != nil {
		log.Printf("❌ Error generating professional chart: %v", err)
		return
	}

	log.Printf("✅ Professional chart generated: %s", outputPath)
}

func testModernGradientChart(generator *charts.ChartGenerator, domains []charts.DomainData) {
	config := charts.CreateModernGradientConfig()
	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"Modern Gradient Style - Maturity Assessment",
		"modern_gradient_chart.png",
		&config,
	)

	if err != nil {
		log.Printf("❌ Error generating modern gradient chart: %v", err)
		return
	}

	log.Printf("✅ Modern gradient chart generated: %s", outputPath)
}

func testHighContrastChart(generator *charts.ChartGenerator, domains []charts.DomainData) {
	config := charts.CreateHighContrastConfig()
	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"High Contrast Style - Maturity Assessment",
		"high_contrast_chart.png",
		&config,
	)

	if err != nil {
		log.Printf("❌ Error generating high contrast chart: %v", err)
		return
	}

	log.Printf("✅ High contrast chart generated: %s", outputPath)
}
