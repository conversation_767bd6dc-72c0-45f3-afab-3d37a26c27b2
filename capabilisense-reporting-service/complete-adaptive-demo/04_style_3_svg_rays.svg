<svg width="1000" height="1000" xmlns="http://www.w3.org/2000/svg"><defs>
			<radialGradient id="rayGradient0" cx="0%" cy="0%" r="100%">
				<stop offset="0%" style="stop-color:rgb(179,55,255);stop-opacity:0.9"/>
				<stop offset="50%" style="stop-color:rgb(138,43,226);stop-opacity:0.7"/>
				<stop offset="100%" style="stop-color:rgb(96,30,158);stop-opacity:0.3"/>
			</radialGradient>
			<linearGradient id="rayLinear0" x1="0%" y1="0%" x2="100%" y2="0%">
				<stop offset="0%" style="stop-color:rgb(138,43,226);stop-opacity:0.1"/>
				<stop offset="30%" style="stop-color:rgb(138,43,226);stop-opacity:0.8"/>
				<stop offset="70%" style="stop-color:rgb(138,43,226);stop-opacity:0.9"/>
				<stop offset="100%" style="stop-color:rgb(138,43,226);stop-opacity:0.6"/>
			</linearGradient>
			<radialGradient id="rayGradient1" cx="0%" cy="0%" r="100%">
				<stop offset="0%" style="stop-color:rgb(97,0,169);stop-opacity:0.9"/>
				<stop offset="50%" style="stop-color:rgb(75,0,130);stop-opacity:0.7"/>
				<stop offset="100%" style="stop-color:rgb(52,0,91);stop-opacity:0.3"/>
			</radialGradient>
			<linearGradient id="rayLinear1" x1="0%" y1="0%" x2="100%" y2="0%">
				<stop offset="0%" style="stop-color:rgb(75,0,130);stop-opacity:0.1"/>
				<stop offset="30%" style="stop-color:rgb(75,0,130);stop-opacity:0.8"/>
				<stop offset="70%" style="stop-color:rgb(75,0,130);stop-opacity:0.9"/>
				<stop offset="100%" style="stop-color:rgb(75,0,130);stop-opacity:0.6"/>
			</linearGradient>
			<radialGradient id="rayGradient2" cx="0%" cy="0%" r="100%">
				<stop offset="0%" style="stop-color:rgb(255,26,191);stop-opacity:0.9"/>
				<stop offset="50%" style="stop-color:rgb(255,20,147);stop-opacity:0.7"/>
				<stop offset="100%" style="stop-color:rgb(178,14,102);stop-opacity:0.3"/>
			</radialGradient>
			<linearGradient id="rayLinear2" x1="0%" y1="0%" x2="100%" y2="0%">
				<stop offset="0%" style="stop-color:rgb(255,20,147);stop-opacity:0.1"/>
				<stop offset="30%" style="stop-color:rgb(255,20,147);stop-opacity:0.8"/>
				<stop offset="70%" style="stop-color:rgb(255,20,147);stop-opacity:0.9"/>
				<stop offset="100%" style="stop-color:rgb(255,20,147);stop-opacity:0.6"/>
			</linearGradient>
			<radialGradient id="rayGradient3" cx="0%" cy="0%" r="100%">
				<stop offset="0%" style="stop-color:rgb(255,89,0);stop-opacity:0.9"/>
				<stop offset="50%" style="stop-color:rgb(255,69,0);stop-opacity:0.7"/>
				<stop offset="100%" style="stop-color:rgb(178,48,0);stop-opacity:0.3"/>
			</radialGradient>
			<linearGradient id="rayLinear3" x1="0%" y1="0%" x2="100%" y2="0%">
				<stop offset="0%" style="stop-color:rgb(255,69,0);stop-opacity:0.1"/>
				<stop offset="30%" style="stop-color:rgb(255,69,0);stop-opacity:0.8"/>
				<stop offset="70%" style="stop-color:rgb(255,69,0);stop-opacity:0.9"/>
				<stop offset="100%" style="stop-color:rgb(255,69,0);stop-opacity:0.6"/>
			</linearGradient>
			<radialGradient id="rayGradient4" cx="0%" cy="0%" r="100%">
				<stop offset="0%" style="stop-color:rgb(255,182,0);stop-opacity:0.9"/>
				<stop offset="50%" style="stop-color:rgb(255,140,0);stop-opacity:0.7"/>
				<stop offset="100%" style="stop-color:rgb(178,98,0);stop-opacity:0.3"/>
			</radialGradient>
			<linearGradient id="rayLinear4" x1="0%" y1="0%" x2="100%" y2="0%">
				<stop offset="0%" style="stop-color:rgb(255,140,0);stop-opacity:0.1"/>
				<stop offset="30%" style="stop-color:rgb(255,140,0);stop-opacity:0.8"/>
				<stop offset="70%" style="stop-color:rgb(255,140,0);stop-opacity:0.9"/>
				<stop offset="100%" style="stop-color:rgb(255,140,0);stop-opacity:0.6"/>
			</linearGradient>
		<filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
			<feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
		</filter>
		<filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
			<feGaussianBlur stdDeviation="3" result="coloredBlur"/>
			<feMerge>
				<feMergeNode in="coloredBlur"/>
				<feMergeNode in="SourceGraphic"/>
			</feMerge>
		</filter></defs><rect width="100%" height="100%" fill="rgb(255,255,255)"/>
			<polygon points="487.50,500.00 512.50,500.00 512.50,472.64 487.50,472.64"
				fill="url(#rayLinear0)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.80"/>
				<circle cx="500.00" cy="471.64" r="2"
					fill="rgba(255,255,255,0.3)"/>
			<polygon points="483.75,472.64 516.25,472.64 516.25,445.28 483.75,445.28"
				fill="url(#rayLinear0)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.70"/>
			<circle cx="500.00" cy="363.20" r="12.50"
				fill="url(#rayGradient0)"
				filter="url(#dropShadow)"/>
			<polygon points="496.14,488.11 503.86,511.89 525.01,505.02 517.29,481.24"
				fill="url(#rayLinear1)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.80"/>
			<circle cx="605.76" cy="465.64" r="12.50"
				fill="url(#rayGradient1)"
				filter="url(#dropShadow)"/>
			<polygon points="510.11,492.65 489.89,507.35 507.47,531.55 527.70,516.86"
				fill="url(#rayLinear2)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.80"/>
				<circle cx="518.17" cy="525.01" r="2"
					fill="rgba(255,255,255,0.3)"/>
			<polygon points="530.73,514.65 504.44,533.76 522.03,557.96 548.32,538.86"
				fill="url(#rayLinear2)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.70"/>
			<circle cx="587.93" cy="621.03" r="12.50"
				fill="url(#rayGradient2)"
				filter="url(#dropShadow)"/>
			<polygon points="510.11,507.35 489.89,492.65 476.06,511.68 496.29,526.38"
				fill="url(#rayLinear3)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.80"/>
			<circle cx="430.88" cy="595.14" r="12.50"
				fill="url(#rayGradient3)"
				filter="url(#dropShadow)"/>
			<polygon points="496.14,511.89 503.86,488.11 479.06,480.05 471.33,503.83"
				fill="url(#rayLinear4)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.80"/>
				<circle cx="474.25" cy="491.63" r="2"
					fill="rgba(255,255,255,0.3)"/>
			<polygon points="470.17,507.40 480.22,476.49 455.41,468.43 445.37,499.34"
				fill="url(#rayLinear4)"
				stroke="none"
				filter="url(#glow)"
				opacity="0.70"/>
			<circle cx="375.98" cy="459.70" r="12.50"
				fill="url(#rayGradient4)"
				filter="url(#dropShadow)"/></svg>