# Codebase Structure: CapabiliSense PDF Reporting Service (Go)

This document outlines the planned directory and file structure for the Go-based PDF reporting service.

## Root Directory: `capabilisense-reporting-service/`

```
capabilisense-reporting-service/
├── cmd/
│   └── reportgenerator/
│       └── main.go                 # Main application entry point (HTTP server or CLI)
├── pkg/
│   ├── config/
│   │   ├── config.go               # App configuration (env vars, flags)
│   │   └── prompts_library.go      # Go lib for prompts_library.json mgmt & LLM client factory
│   ├── dataextraction/
│   │   ├── models.go               # Go structs for AnalysisResults, FrameworkData etc.
│   │   ├── extractor.go            # Interface/impl for fetching raw data (DB/API)
│   │   └── processor.go            # Business logic for data aggregation & prep for AI
│   ├── aiinsights/
│   │   ├── llmclient.go            # LLM interaction interface (using prompts_library.go)
│   │   ├── providers/              # Specific LLM provider SDK wrappers
│   │   │   ├── openai.go
│   │   │   └── google.go
│   │   ├── insightgenerator.go     # Orchestrates LLM calls for report sections
│   │   └── models.go               # Go structs for structured LLM outputs
│   ├── pdfgenerator/
│   │   ├── renderer.go             # PDF Renderer interface
│   │   ├── simple_renderer.go      # Direct fpdf implementation (PRIMARY)
│   │   ├── mdtopdf_renderer.go     # Impl using mdtopdf (debugging/secondary)
│   │   ├── template_manager.go     # Manages Go text/template for Markdown/HTML
│   │   └── (future) headlesschrome_renderer.go
│   ├── utils/
│   │   └── stringutils.go          # ASCII-fication, text helpers
│   └── models/
│       └── report_data.go          # Struct for final enriched data for templates
├── configs/
│   ├── prompts_library.json
│   └── fallback_insights/
│       └── default_insight.xml     # Example fallback
├── templates/
│   └── assessment_report_simplified.md # Current Markdown template
├── .env
├── go.mod
├── go.sum
└── README.md
```

## Key Package Responsibilities:

*   **`cmd/reportgenerator`**: Initializes and runs the service. Handles HTTP requests or CLI commands.
*   **`pkg/config`**: Manages all configurations, including parsing `prompts_library.json`.
*   **`pkg/dataextraction`**: Stage A - Fetches raw assessment and framework data, performs algorithmic derivations (median score, top/bottom domains), and prepares data structures for AI processing.
*   **`pkg/aiinsights`**: Stage B - Interacts with LLMs via the `prompts_library` configuration to generate all textual insights. Handles structured output and fallbacks.
*   **`pkg/pdfgenerator`**: Stage C - Takes final enriched data and uses a `Renderer` implementation to produce the PDF byte stream. **Dual renderer approach:**
    *   **`simple_renderer.go`** (PRIMARY): Direct fpdf implementation for reliable PDF generation with professional layout
    *   **`mdtopdf_renderer.go`** (SECONDARY): Uses mdtopdf library with Markdown templates (debugging/future use)
    *   **`template_manager.go`**: Manages Go text/template for Markdown generation (used by mdtopdf renderer)
*   **`pkg/models`**: Contains the Go struct `ReportData` which is the comprehensive data object passed to the templating engine. Other shared simple models can go here if not fitting better elsewhere.
*   **`pkg/utils`**: Common utility functions.
*   **`configs/`**: External configuration files for prompts and fallbacks.
*   **`templates/`**: Report templates (currently Markdown).

## Module Initialization:

The service is initialized as a Go module:
```bash
cd capabilisense-reporting-service
go mod init <your_chosen_module_path> # e.g., github.com/your-org/capabilisense-reporting-service
```