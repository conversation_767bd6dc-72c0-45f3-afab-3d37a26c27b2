package charts

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"
)

// AdaptiveTexturedRays<PERSON><PERSON> combines textured rays with adaptive scaling
type AdaptiveTexturedRaysChart struct {
	config         RaysChartConfig
	adaptiveConfig AdaptiveScalingConfig
	fontRenderer   *EnhancedFontRenderer
}

// NewAdaptiveTexturedRays<PERSON><PERSON> creates a new adaptive textured rays chart generator
func NewAdaptiveTexturedRaysChart(config RaysChartConfig, adaptiveConfig AdaptiveScalingConfig) *AdaptiveTexturedRaysChart {
	return &AdaptiveTexturedRaysChart{
		config:         config,
		adaptiveConfig: adaptiveConfig,
		fontRenderer:   NewEnhancedFontRenderer(),
	}
}

// Generate creates an adaptive textured rays chart with intelligent scaling
func (atrc *AdaptiveTexturedRaysChart) Generate(data SpiderChartData, title, outputPath string) error {
	// Calculate adaptive scaling
	scaling := CalculateAdaptiveScaling(data, atrc.adaptiveConfig)

	// Create image
	img := image.NewRGBA(image.Rect(0, 0, atrc.config.Width, atrc.config.Height))

	// Fill textured background
	atrc.drawTexturedBackground(img)

	// Skip title drawing for cleaner charts
	// Title and zoom info removed per user request

	// Draw adaptive grid
	if atrc.config.ShowGrid {
		atrc.drawAdaptiveGrid(img, data, scaling)
	}

	// Draw unused range indicator (red gradient with texture)
	if scaling.IsZoomed && atrc.adaptiveConfig.ShowUnusedRange {
		atrc.drawTexturedUnusedRange(img, scaling)
	}

	// Draw adaptive textured rays
	atrc.drawAdaptiveTexturedRays(img, data, scaling)

	// Draw adaptive labels
	if atrc.config.ShowLabels {
		atrc.drawAdaptiveLabels(img, data, scaling)
	}

	// Skip zoom indicator for cleaner charts
	// Zoom indicator removed per user request

	// Save image
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return fmt.Errorf("failed to encode PNG: %w", err)
	}

	return nil
}

// drawTexturedBackground creates a subtle textured background
func (atrc *AdaptiveTexturedRaysChart) drawTexturedBackground(img *image.RGBA) {
	bounds := img.Bounds()

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			// Create subtle noise texture
			noise := atrc.perlinNoise(float64(x)*0.01, float64(y)*0.01) * 0.05

			baseColor := atrc.config.BackgroundColor
			r := uint8(math.Max(0, math.Min(255, float64(baseColor.R)*(1.0+noise))))
			g := uint8(math.Max(0, math.Min(255, float64(baseColor.G)*(1.0+noise))))
			b := uint8(math.Max(0, math.Min(255, float64(baseColor.B)*(1.0+noise))))

			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
}

// drawTitle draws title with optional zoom information using enhanced fonts
func (atrc *AdaptiveTexturedRaysChart) drawTitle(img *image.RGBA, title string, scaling ScalingResult) {
	// Use large, bold font for title
	titleConfig := TitleFontConfig()
	titleConfig.Color = atrc.config.TextColor

	// Measure title to center it properly
	titleWidth, titleHeight := atrc.fontRenderer.MeasureText(title, titleConfig)
	titleX := atrc.config.Width/2 - titleWidth/2
	titleY := 40 // Reduced space from top to leave more room for labels

	// Ensure title stays within bounds
	if titleX < 10 {
		titleX = 10
	}
	if titleX+titleWidth > atrc.config.Width-10 {
		titleX = atrc.config.Width - titleWidth - 10
	}

	atrc.fontRenderer.DrawText(img, titleX, titleY, title, titleConfig)

	// Add zoom info if zoomed with smaller font
	if scaling.IsZoomed && atrc.adaptiveConfig.ShowZoomIndicator {
		zoomInfo := fmt.Sprintf("(Adaptive view: 0-%.1f of 0-%.0f scale)",
			scaling.ZoomedRange, scaling.UnusedRangeEnd)

		// Use medium font for zoom info
		zoomConfig := LargeFontConfig()
		zoomConfig.Color = atrc.adaptiveConfig.ZoomIndicatorColor
		zoomConfig.Size = FontSizeMedium

		zoomWidth, _ := atrc.fontRenderer.MeasureText(zoomInfo, zoomConfig)
		zoomX := atrc.config.Width/2 - zoomWidth/2
		zoomY := titleY + titleHeight + 5 // Reduced spacing

		// Ensure zoom info stays within bounds
		if zoomX < 10 {
			zoomX = 10
		}
		if zoomX+zoomWidth > atrc.config.Width-10 {
			zoomX = atrc.config.Width - zoomWidth - 10
		}

		atrc.fontRenderer.DrawText(img, zoomX, zoomY, zoomInfo, zoomConfig)
	}
}

// drawAdaptiveGrid draws grid with both zoomed and original levels
func (atrc *AdaptiveTexturedRaysChart) drawAdaptiveGrid(img *image.RGBA, data SpiderChartData, scaling ScalingResult) {
	centerX, centerY := atrc.config.CenterX, atrc.config.CenterY
	gridInfo := GenerateAdaptiveGrid(scaling, atrc.adaptiveConfig, atrc.config.MinRadius, atrc.config.MaxRadius)

	// Skip original grid levels to avoid confusion and overlap
	// Original levels (3, 4, etc.) are confusing when overlapping with zoomed levels (0.5, 1.0, 1.5)
	// Only show the relevant zoomed grid for clarity

	// Draw zoomed grid levels (prominent)
	for _, level := range gridInfo.ZoomedLevels {
		atrc.drawTexturedCircle(img, centerX, centerY, level.Radius, level.Color, level.Width, 1.0)

		// Draw level label with larger font - positioned clearly
		if atrc.config.ShowValues {
			labelConfig := DefaultFontConfig()
			labelConfig.Color = atrc.config.TextColor
			labelConfig.Size = FontSizeLarge // Larger axis values
			labelConfig.Outline = false      // Clean rendering

			// Position labels at top (12 o'clock) for clear, non-overlapping display
			labelX := centerX - 10 // Slightly left of center
			labelY := centerY - level.Radius - 10 // Above the circle
			atrc.fontRenderer.DrawText(img, labelX, labelY, level.Label, labelConfig)
		}
	}
}

// drawTexturedUnusedRange draws textured red gradient for unused range
func (atrc *AdaptiveTexturedRaysChart) drawTexturedUnusedRange(img *image.RGBA, scaling ScalingResult) {
	centerX, centerY := atrc.config.CenterX, atrc.config.CenterY

	// Calculate where unused range starts in pixels
	usedRangeRadius := int(ApplyAdaptiveRadius(scaling.ZoomedRange, scaling, atrc.config.MinRadius, atrc.config.MaxRadius))

	// Draw textured concentric rings with red gradient
	numRings := (atrc.config.MaxRadius - usedRangeRadius) / int(atrc.adaptiveConfig.UnusedRangeWidth)
	if numRings < 1 {
		numRings = 1
	}

	for ring := 0; ring < numRings; ring++ {
		ringRadius := usedRangeRadius + ring*int(atrc.adaptiveConfig.UnusedRangeWidth)
		if ringRadius > atrc.config.MaxRadius {
			ringRadius = atrc.config.MaxRadius
		}

		// Calculate opacity - stronger near the used range
		ringRatio := float64(ring) / float64(numRings)
		opacity := atrc.adaptiveConfig.UnusedRangeIntensity * (1.0 - ringRatio*0.4)

		ringColor := color.RGBA{
			R: atrc.adaptiveConfig.UnusedRangeColor.R,
			G: atrc.adaptiveConfig.UnusedRangeColor.G,
			B: atrc.adaptiveConfig.UnusedRangeColor.B,
			A: uint8(float64(atrc.adaptiveConfig.UnusedRangeColor.A) * opacity),
		}

		// Draw textured ring with procedural pattern
		atrc.drawTexturedRing(img, centerX, centerY, ringRadius, ringColor, atrc.adaptiveConfig.UnusedRangeWidth, ring)
	}
}

// drawAdaptiveTexturedRays draws textured rays with adaptive scaling
func (atrc *AdaptiveTexturedRaysChart) drawAdaptiveTexturedRays(img *image.RGBA, data SpiderChartData, scaling ScalingResult) {
	centerX := float64(atrc.config.CenterX)
	centerY := float64(atrc.config.CenterY)
	numDomains := len(data.Domains)

	if numDomains == 0 {
		return
	}

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2

		// Calculate ray length using adaptive scaling
		rayLength := ApplyAdaptiveRadius(domain.Score, scaling, atrc.config.MinRadius, atrc.config.MaxRadius)

		// Get adaptive ray color
		baseColor := atrc.getRayColor(i, domain.Score, data.MaxLevel)
		rayColor := GetAdaptiveRayColor(baseColor, domain.Score, scaling, atrc.adaptiveConfig)

		// Draw textured ray with scale pattern and adaptive scaling
		atrc.drawAdaptiveScalePatternRay(img, centerX, centerY, angle, rayLength, rayColor, domain.Score, scaling)
	}
}

// drawAdaptiveScalePatternRay creates a ray with scale-like texture pattern and adaptive scaling
func (atrc *AdaptiveTexturedRaysChart) drawAdaptiveScalePatternRay(img *image.RGBA, centerX, centerY, angle, length float64,
	rayColor color.RGBA, score float64, scaling ScalingResult) {

	// Number of scales based on score and adaptive scaling
	baseScales := int(math.Ceil(score * 3))
	if scaling.IsZoomed {
		// For zoomed view, increase scale density for better visual impact
		scaleFactor := scaling.UnusedRangeEnd / scaling.ZoomedRange
		baseScales = int(math.Ceil(score * 3 * math.Min(scaleFactor, 2.0)))
	}

	if baseScales > int(scaling.UnusedRangeEnd)*3 {
		baseScales = int(scaling.UnusedRangeEnd) * 3
	}

	scaleLength := length / float64(baseScales)
	baseWidth := atrc.config.RayWidth

	// Enhance width for low scores in zoomed view
	if scaling.IsZoomed && score < scaling.ZoomedRange*0.5 {
		baseWidth *= 1.2 // Make rays slightly thicker for better visibility
	}

	for scale := 0; scale < baseScales; scale++ {
		// Calculate scale properties
		scaleStart := float64(scale) * scaleLength
		scaleEnd := float64(scale+1) * scaleLength

		// Expanding width for each scale
		widthMultiplier := 1.0 + float64(scale)*0.25
		scaleWidth := baseWidth * widthMultiplier

		// Color variation for each scale with adaptive intensity
		colorVariation := 1.0 - float64(scale)*0.06
		if scaling.IsZoomed {
			// Enhance color intensity for zoomed view
			colorVariation = math.Max(colorVariation, 0.7)
		}

		scaleColor := color.RGBA{
			R: uint8(float64(rayColor.R) * colorVariation),
			G: uint8(float64(rayColor.G) * colorVariation),
			B: uint8(float64(rayColor.B) * colorVariation),
			A: rayColor.A,
		}

		// Draw individual scale with enhanced texture
		atrc.drawAdaptiveTexturedScale(img, centerX, centerY, angle, scaleStart, scaleEnd, scaleWidth, scaleColor, scale, scaling)
	}
}

// drawAdaptiveTexturedScale draws a single scale with procedural texture and adaptive enhancements
func (atrc *AdaptiveTexturedRaysChart) drawAdaptiveTexturedScale(img *image.RGBA, centerX, centerY, angle, start, end, width float64,
	col color.RGBA, scaleIndex int, scaling ScalingResult) {

	// Calculate scale boundaries
	startX := centerX + start*math.Cos(angle)
	startY := centerY + start*math.Sin(angle)
	endX := centerX + end*math.Cos(angle)
	endY := centerY + end*math.Sin(angle)

	// Perpendicular direction for width
	perpX := -math.Sin(angle)
	perpY := math.Cos(angle)

	// Enhanced steps for better quality in zoomed view
	steps := int(end - start)
	if scaling.IsZoomed {
		steps = int(math.Max(float64(steps), 15)) // Minimum steps for smooth appearance
	}
	if steps < 10 {
		steps = 10
	}

	for step := 0; step < steps; step++ {
		t := float64(step) / float64(steps)

		// Position along scale
		x := startX + t*(endX-startX)
		y := startY + t*(endY-startY)

		// Enhanced diamond shape for better visual impact
		widthFactor := 1.0 - math.Abs(t-0.5)*1.8 // More pronounced diamond shape
		if widthFactor < 0.2 {
			widthFactor = 0.2 // Minimum width for visibility
		}
		currentWidth := width * widthFactor

		// Draw cross-section with enhanced texture
		for w := -currentWidth / 2; w <= currentWidth/2; w += 0.4 {
			px := x + w*perpX
			py := y + w*perpY

			// Apply enhanced texture based on position and scaling
			textureValue := atrc.adaptiveScaleTexture(px, py, float64(scaleIndex), scaling)

			// Modify color based on texture
			texturedColor := color.RGBA{
				R: uint8(math.Max(0, math.Min(255, float64(col.R)*textureValue))),
				G: uint8(math.Max(0, math.Min(255, float64(col.G)*textureValue))),
				B: uint8(math.Max(0, math.Min(255, float64(col.B)*textureValue))),
				A: col.A,
			}

			atrc.setPixel(img, int(px), int(py), texturedColor)
		}
	}

	// Add enhanced scale edge highlights
	atrc.drawAdaptiveScaleEdges(img, startX, startY, endX, endY, width, perpX, perpY, col, scaling)
}

// adaptiveScaleTexture generates enhanced procedural texture for scales with adaptive scaling
func (atrc *AdaptiveTexturedRaysChart) adaptiveScaleTexture(x, y float64, scaleIndex float64, scaling ScalingResult) float64 {
	// Enhanced texture patterns for better visual impact

	// Base pattern - creates scale-like ridges
	ridgePattern := math.Sin(x*0.12+scaleIndex) * math.Sin(y*0.12+scaleIndex)

	// Fine detail noise
	detailNoise := atrc.perlinNoise(x*0.06, y*0.06)

	// Radial pattern from center
	centerX := float64(atrc.config.CenterX)
	centerY := float64(atrc.config.CenterY)
	distance := math.Sqrt((x-centerX)*(x-centerX) + (y-centerY)*(y-centerY))
	radialPattern := math.Sin(distance * 0.025)

	// Enhanced pattern for zoomed view
	zoomEnhancement := 1.0
	if scaling.IsZoomed {
		zoomEnhancement = 1.2 // Enhance texture contrast for zoomed view
	}

	// Combine patterns with adaptive enhancement
	texture := 0.75 + ridgePattern*0.2*zoomEnhancement + detailNoise*0.12 + radialPattern*0.08

	// Clamp to valid range
	return math.Max(0.4, math.Min(1.3, texture))
}

// drawAdaptiveScaleEdges adds enhanced highlights to scale edges
func (atrc *AdaptiveTexturedRaysChart) drawAdaptiveScaleEdges(img *image.RGBA, startX, startY, endX, endY, width, perpX, perpY float64,
	col color.RGBA, scaling ScalingResult) {

	// Enhanced edge color
	edgeIntensity := 1.4
	if scaling.IsZoomed {
		edgeIntensity = 1.6 // Brighter edges for zoomed view
	}

	edgeColor := color.RGBA{
		R: uint8(math.Min(255, float64(col.R)*edgeIntensity)),
		G: uint8(math.Min(255, float64(col.G)*edgeIntensity)),
		B: uint8(math.Min(255, float64(col.B)*edgeIntensity)),
		A: uint8(float64(col.A) * 0.9),
	}

	// Draw enhanced edges
	steps := 20
	if scaling.IsZoomed {
		steps = 30 // More steps for smoother edges in zoomed view
	}

	for i := 0; i <= steps; i++ {
		t := float64(i) / float64(steps)
		x := startX + t*(endX-startX)
		y := startY + t*(endY-startY)

		// Width varies along length
		widthFactor := 1.0 - math.Abs(t-0.5)*1.8
		if widthFactor < 0.2 {
			widthFactor = 0.2
		}
		currentWidth := width * widthFactor

		// Top edge
		topX := x + (currentWidth/2)*perpX
		topY := y + (currentWidth/2)*perpY
		atrc.setPixel(img, int(topX), int(topY), edgeColor)

		// Bottom edge
		bottomX := x - (currentWidth/2)*perpX
		bottomY := y - (currentWidth/2)*perpY
		atrc.setPixel(img, int(bottomX), int(bottomY), edgeColor)
	}
}

// drawAdaptiveLabels draws domain labels with adaptive information using enhanced fonts
func (atrc *AdaptiveTexturedRaysChart) drawAdaptiveLabels(img *image.RGBA, data SpiderChartData, scaling ScalingResult) {
	centerX, centerY := atrc.config.CenterX, atrc.config.CenterY

	// Calculate safe label radius that keeps labels within image bounds
	imgBounds := img.Bounds()
	maxDistanceFromCenter := min(
		min(centerX-imgBounds.Min.X, imgBounds.Max.X-centerX),
		min(centerY-imgBounds.Min.Y, imgBounds.Max.Y-centerY),
	) - 80 // Leave margin for text

	labelRadius := float64(atrc.config.MaxRadius) + 100 // Much more spacing to avoid ray overlay
	if labelRadius > float64(maxDistanceFromCenter) {
		labelRadius = float64(maxDistanceFromCenter) // Constrain to image bounds
	}

	numDomains := len(data.Domains)

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2

		labelText := atrc.formatAdaptiveLabel(domain, scaling)

		// Create font configuration for labels with clean rendering
		labelConfig := LargeFontConfig()
		labelConfig.Size = FontSizeXLarge // Extra large, very readable font
		labelConfig.Outline = false       // Clean rendering without outline
		labelConfig.Shadow = false        // Clean rendering without shadow

		// Color-coding for adaptive scaling
		if scaling.IsZoomed {
			scoreRatio := domain.Score / scaling.UnusedRangeEnd
			if scoreRatio < 0.25 {
				labelConfig.Color = color.RGBA{220, 53, 69, 255} // Red for very low
			} else if scoreRatio < 0.5 {
				labelConfig.Color = color.RGBA{255, 193, 7, 255} // Yellow for low
			} else if scoreRatio < 0.75 {
				labelConfig.Color = color.RGBA{40, 167, 69, 255} // Green for good
			} else {
				labelConfig.Color = atrc.config.TextColor // Default for high
			}
		} else {
			labelConfig.Color = atrc.config.TextColor
		}

		// Measure text for proper positioning
		textWidth, textHeight := atrc.fontRenderer.MeasureText(labelText, labelConfig)

		// Calculate base position
		baseX := centerX + int(labelRadius*math.Cos(angle))
		baseY := centerY + int(labelRadius*math.Sin(angle))

		// Adjust position based on angle for better readability and to avoid ray overlay
		var labelX, labelY int
		imgBounds := img.Bounds()

		// Add extra spacing to avoid ray overlay
		extraSpacing := 20

		if angle > math.Pi/4 && angle < 3*math.Pi/4 {
			// Bottom labels - center horizontally, position well below point
			labelX = baseX - textWidth/2
			labelY = baseY + extraSpacing
		} else if angle >= 3*math.Pi/4 || angle <= -3*math.Pi/4 {
			// Left labels - right align with extra spacing, center vertically
			labelX = baseX - textWidth - extraSpacing
			labelY = baseY + textHeight/4
		} else if angle > -3*math.Pi/4 && angle < -math.Pi/4 {
			// Top labels - center horizontally, position well above point
			labelX = baseX - textWidth/2
			labelY = baseY - textHeight - extraSpacing
		} else {
			// Right labels - left align with extra spacing, center vertically
			labelX = baseX + extraSpacing
			labelY = baseY + textHeight/4
		}

		// Ensure labels stay within image bounds
		if labelX < imgBounds.Min.X {
			labelX = imgBounds.Min.X + 5
		}
		if labelX+textWidth > imgBounds.Max.X {
			labelX = imgBounds.Max.X - textWidth - 5
		}
		if labelY < imgBounds.Min.Y+40 { // More top margin to prevent label cutoff
			labelY = imgBounds.Min.Y + 40
		}
		if labelY+textHeight > imgBounds.Max.Y {
			labelY = imgBounds.Max.Y - textHeight - 5
		}

		atrc.fontRenderer.DrawMultilineText(img, labelX, labelY, labelText, labelConfig)
	}
}

func (atrc *AdaptiveTexturedRaysChart) formatAdaptiveLabel(domain DomainData, scaling ScalingResult) string {
	if !atrc.adaptiveConfig.ShowActualRange {
		return domain.Name
	}

	if scaling.IsZoomed {
		// Use simple characters to avoid rendering issues
		return fmt.Sprintf("%s\n%.1f / %.0f", domain.Name, domain.Score, scaling.UnusedRangeEnd)
	} else {
		// Use parentheses instead of special characters
		return fmt.Sprintf("%s\n(%.1f)", domain.Name, domain.Score)
	}
}

func (atrc *AdaptiveTexturedRaysChart) drawZoomIndicator(img *image.RGBA, scaling ScalingResult) {
	// Use clean fonts for zoom indicator
	indicatorConfig := DefaultFontConfig()
	indicatorConfig.Color = atrc.adaptiveConfig.ZoomIndicatorColor
	indicatorConfig.Size = FontSizeMedium
	indicatorConfig.Outline = false // Clean rendering
	indicatorConfig.Shadow = false  // Clean rendering

	zoomText := fmt.Sprintf("Adaptive View: 0-%.1f of 0-%.0f", scaling.ZoomedRange, scaling.UnusedRangeEnd)
	contextText := fmt.Sprintf("Red area: %.1f-%.0f (improvement needed)", scaling.ZoomedRange, scaling.UnusedRangeEnd)

	// Measure text for proper positioning
	zoomWidth, zoomHeight := atrc.fontRenderer.MeasureText(zoomText, indicatorConfig)
	contextWidth, contextHeight := atrc.fontRenderer.MeasureText(contextText, indicatorConfig)

	// Position in bottom right with proper spacing
	indicatorX := atrc.config.Width - int(math.Max(float64(zoomWidth), float64(contextWidth))) - 20
	indicatorY := atrc.config.Height - zoomHeight - contextHeight - 30

	atrc.fontRenderer.DrawText(img, indicatorX, indicatorY, zoomText, indicatorConfig)
	atrc.fontRenderer.DrawText(img, indicatorX, indicatorY+zoomHeight+5, contextText, indicatorConfig)
}

// Helper methods
func (atrc *AdaptiveTexturedRaysChart) getRayColor(index int, score float64, maxLevel int) color.RGBA {
	if len(atrc.config.RayColors) == 0 {
		return color.RGBA{100, 100, 100, 200}
	}
	return atrc.config.RayColors[index%len(atrc.config.RayColors)]
}

func (atrc *AdaptiveTexturedRaysChart) perlinNoise(x, y float64) float64 {
	xi := int(math.Floor(x))
	yi := int(math.Floor(y))
	xf := x - float64(xi)
	yf := y - float64(yi)

	a := atrc.randomValue(xi, yi)
	b := atrc.randomValue(xi+1, yi)
	c := atrc.randomValue(xi, yi+1)
	d := atrc.randomValue(xi+1, yi+1)

	i1 := atrc.interpolate(a, b, xf)
	i2 := atrc.interpolate(c, d, xf)
	return atrc.interpolate(i1, i2, yf)
}

func (atrc *AdaptiveTexturedRaysChart) randomValue(x, y int) float64 {
	n := x + y*57
	n = (n << 13) ^ n
	return (1.0 - float64((n*(n*n*15731+789221)+1376312589)&0x7fffffff)/1073741824.0)
}

func (atrc *AdaptiveTexturedRaysChart) interpolate(a, b, t float64) float64 {
	ft := t * math.Pi
	f := (1.0 - math.Cos(ft)) * 0.5
	return a*(1.0-f) + b*f
}

// drawText method removed - now using enhanced font renderer

func (atrc *AdaptiveTexturedRaysChart) setPixel(img *image.RGBA, x, y int, col color.RGBA) {
	bounds := img.Bounds()
	if x >= bounds.Min.X && x < bounds.Max.X && y >= bounds.Min.Y && y < bounds.Max.Y {
		img.Set(x, y, col)
	}
}

// drawTexturedCircle draws a circle with subtle texture
func (atrc *AdaptiveTexturedRaysChart) drawTexturedCircle(img *image.RGBA, centerX, centerY, radius int, col color.RGBA, lineWidth, intensity float64) {
	for angle := 0.0; angle < 2*math.Pi; angle += 0.01 {
		x := centerX + int(float64(radius)*math.Cos(angle))
		y := centerY + int(float64(radius)*math.Sin(angle))

		// Add subtle texture to grid lines
		textureValue := atrc.perlinNoise(float64(x)*0.02, float64(y)*0.02)*0.1 + 0.9
		texturedColor := color.RGBA{
			R: uint8(float64(col.R) * textureValue * intensity),
			G: uint8(float64(col.G) * textureValue * intensity),
			B: uint8(float64(col.B) * textureValue * intensity),
			A: col.A,
		}

		thickness := int(lineWidth)
		for dx := -thickness; dx <= thickness; dx++ {
			for dy := -thickness; dy <= thickness; dy++ {
				if dx*dx+dy*dy <= thickness*thickness {
					px, py := x+dx, y+dy
					if px >= 0 && py >= 0 && px < img.Bounds().Dx() && py < img.Bounds().Dy() {
						atrc.setPixelBlended(img, px, py, texturedColor)
					}
				}
			}
		}
	}
}

// drawTexturedRing draws a textured ring for unused range indication
func (atrc *AdaptiveTexturedRaysChart) drawTexturedRing(img *image.RGBA, centerX, centerY, radius int, col color.RGBA, width float64, ringIndex int) {
	innerRadius := radius - int(width/2)
	outerRadius := radius + int(width/2)

	for angle := 0.0; angle < 2*math.Pi; angle += 0.01 {
		for r := innerRadius; r <= outerRadius; r++ {
			x := centerX + int(float64(r)*math.Cos(angle))
			y := centerY + int(float64(r)*math.Sin(angle))

			if x >= 0 && y >= 0 && x < img.Bounds().Dx() && y < img.Bounds().Dy() {
				// Add texture pattern to unused range rings
				textureValue := atrc.adaptiveScaleTexture(float64(x), float64(y), float64(ringIndex), ScalingResult{IsZoomed: true})
				texturedColor := color.RGBA{
					R: uint8(float64(col.R) * textureValue),
					G: uint8(float64(col.G) * textureValue),
					B: uint8(float64(col.B) * textureValue),
					A: col.A,
				}
				atrc.setPixelBlended(img, x, y, texturedColor)
			}
		}
	}
}

// setPixelBlended sets a pixel with alpha blending
func (atrc *AdaptiveTexturedRaysChart) setPixelBlended(img *image.RGBA, x, y int, newColor color.RGBA) {
	if x < 0 || y < 0 || x >= img.Bounds().Dx() || y >= img.Bounds().Dy() {
		return
	}

	existing := img.RGBAAt(x, y)
	alpha := float64(newColor.A) / 255.0
	invAlpha := 1.0 - alpha

	blended := color.RGBA{
		R: uint8(float64(newColor.R)*alpha + float64(existing.R)*invAlpha),
		G: uint8(float64(newColor.G)*alpha + float64(existing.G)*invAlpha),
		B: uint8(float64(newColor.B)*alpha + float64(existing.B)*invAlpha),
		A: uint8(math.Max(float64(existing.A), float64(newColor.A))),
	}

	img.Set(x, y, blended)
}
