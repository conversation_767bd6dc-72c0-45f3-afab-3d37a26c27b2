package charts

import (
	"image"
	"image/color"
	"math"
	"strings"
	"unicode"

	"golang.org/x/image/font"
	"golang.org/x/image/font/inconsolata"
	"golang.org/x/image/math/fixed"
)

// FontSize represents different font sizes
type FontSize int

const (
	FontSizeSmall  FontSize = 12
	FontSizeMedium FontSize = 16
	FontSizeLarge  FontSize = 20
	FontSizeXLarge FontSize = 24
	FontSizeTitle  FontSize = 28
)

// FontStyle represents different font styles
type FontStyle int

const (
	FontStyleRegular FontStyle = iota
	FontStyleBold
	FontStyleItalic
)

// EnhancedFontRenderer provides better font rendering without external font files
type EnhancedFontRenderer struct {
	// Available built-in fonts
	regularFont font.Face
	boldFont    font.Face
	titleFont   font.Face
}

// NewEnhancedFontRenderer creates a new enhanced font renderer
func NewEnhancedFontRenderer() *EnhancedFontRenderer {
	return &EnhancedFontRenderer{
		regularFont: inconsolata.Regular8x16, // Better than basicfont
		boldFont:    inconsolata.Bold8x16,    // Bold variant
		titleFont:   inconsolata.Bold8x16,    // Use bold for titles
	}
}

// FontConfig holds font rendering configuration
type FontConfig struct {
	Size         FontSize
	Style        FontStyle
	Color        color.RGBA
	Outline      bool
	OutlineColor color.RGBA
	Shadow       bool
	ShadowColor  color.RGBA
	ShadowOffset int
	AntiAlias    bool
}

// DefaultFontConfig returns sensible font defaults
func DefaultFontConfig() FontConfig {
	return FontConfig{
		Size:         FontSizeMedium,
		Style:        FontStyleRegular,
		Color:        color.RGBA{33, 37, 41, 255}, // Dark gray
		Outline:      true,
		OutlineColor: color.RGBA{255, 255, 255, 200}, // White outline
		Shadow:       false,
		ShadowColor:  color.RGBA{0, 0, 0, 100},
		ShadowOffset: 1,
		AntiAlias:    true,
	}
}

// LargeFontConfig returns configuration for large, readable fonts
func LargeFontConfig() FontConfig {
	return FontConfig{
		Size:         FontSizeLarge,
		Style:        FontStyleRegular,
		Color:        color.RGBA{33, 37, 41, 255},
		Outline:      true,
		OutlineColor: color.RGBA{255, 255, 255, 220},
		Shadow:       true,
		ShadowColor:  color.RGBA{0, 0, 0, 80},
		ShadowOffset: 2,
		AntiAlias:    true,
	}
}

// TitleFontConfig returns configuration for title text
func TitleFontConfig() FontConfig {
	return FontConfig{
		Size:         FontSizeTitle,
		Style:        FontStyleBold,
		Color:        color.RGBA{33, 37, 41, 255},
		Outline:      true,
		OutlineColor: color.RGBA{255, 255, 255, 240},
		Shadow:       true,
		ShadowColor:  color.RGBA{0, 0, 0, 100},
		ShadowOffset: 3,
		AntiAlias:    true,
	}
}

// DrawText renders text with enhanced styling and proper character handling
func (efr *EnhancedFontRenderer) DrawText(img *image.RGBA, x, y int, text string, config FontConfig) {
	// Clean and prepare text
	cleanText := efr.cleanText(text)

	// Select appropriate font
	selectedFont := efr.selectFont(config)

	// Calculate scaling factor for size
	scaleFactor := efr.getScaleFactor(config.Size)

	// Draw shadow first if enabled
	if config.Shadow {
		shadowX := x + config.ShadowOffset
		shadowY := y + config.ShadowOffset
		efr.drawTextLayer(img, shadowX, shadowY, cleanText, selectedFont, config.ShadowColor, scaleFactor)
	}

	// Draw outline if enabled
	if config.Outline {
		efr.drawTextOutline(img, x, y, cleanText, selectedFont, config.OutlineColor, scaleFactor)
	}

	// Draw main text
	efr.drawTextLayer(img, x, y, cleanText, selectedFont, config.Color, scaleFactor)
}

// DrawMultilineText handles text with line breaks
func (efr *EnhancedFontRenderer) DrawMultilineText(img *image.RGBA, x, y int, text string, config FontConfig) {
	lines := strings.Split(text, "\n")
	lineHeight := efr.getLineHeight(config.Size)

	for i, line := range lines {
		lineY := y + i*lineHeight
		efr.DrawText(img, x, lineY, line, config)
	}
}

// MeasureText returns the approximate width and height of text
func (efr *EnhancedFontRenderer) MeasureText(text string, config FontConfig) (width, height int) {
	cleanText := efr.cleanText(text)
	scaleFactor := efr.getScaleFactor(config.Size)

	// Approximate measurements based on font characteristics
	charWidth := int(8 * scaleFactor)   // Base character width
	charHeight := int(16 * scaleFactor) // Base character height

	lines := strings.Split(cleanText, "\n")
	maxWidth := 0

	for _, line := range lines {
		lineWidth := len(line) * charWidth
		if lineWidth > maxWidth {
			maxWidth = lineWidth
		}
	}

	totalHeight := len(lines) * charHeight
	return maxWidth, totalHeight
}

// cleanText removes problematic characters and handles special cases
func (efr *EnhancedFontRenderer) cleanText(text string) string {
	var result strings.Builder

	for _, r := range text {
		switch {
		case r == '(' || r == ')':
			result.WriteRune(r)
		case r == '.' || r == ',':
			result.WriteRune(r)
		case r == '/' || r == '-':
			result.WriteRune(r)
		case r == ':' || r == ';':
			result.WriteRune(r)
		case r == ' ':
			result.WriteRune(r)
		case unicode.IsLetter(r) || unicode.IsDigit(r):
			result.WriteRune(r)
		case r == '\n':
			result.WriteRune(r)
		default:
			// Replace problematic characters with safe alternatives
			if r > 127 {
				result.WriteRune('?') // Replace non-ASCII with ?
			} else {
				result.WriteRune(r) // Keep ASCII characters
			}
		}
	}

	return result.String()
}

// selectFont chooses the appropriate font based on style
func (efr *EnhancedFontRenderer) selectFont(config FontConfig) font.Face {
	switch config.Style {
	case FontStyleBold:
		return efr.boldFont
	case FontStyleItalic:
		return efr.regularFont // Use regular for italic (no italic available)
	default:
		return efr.regularFont
	}
}

// getScaleFactor returns scaling factor for different font sizes
func (efr *EnhancedFontRenderer) getScaleFactor(size FontSize) float64 {
	switch size {
	case FontSizeSmall:
		return 1.0
	case FontSizeMedium:
		return 1.5
	case FontSizeLarge:
		return 2.0
	case FontSizeXLarge:
		return 2.5
	case FontSizeTitle:
		return 3.0
	default:
		return 1.5
	}
}

// getLineHeight returns line height for different font sizes
func (efr *EnhancedFontRenderer) getLineHeight(size FontSize) int {
	switch size {
	case FontSizeSmall:
		return 16
	case FontSizeMedium:
		return 24
	case FontSizeLarge:
		return 32
	case FontSizeXLarge:
		return 40
	case FontSizeTitle:
		return 48
	default:
		return 24
	}
}

// drawTextLayer draws a single layer of text (main, shadow, or outline)
func (efr *EnhancedFontRenderer) drawTextLayer(img *image.RGBA, x, y int, text string, face font.Face, col color.RGBA, scale float64) {
	if scale <= 1.0 {
		// Use standard rendering for small text
		efr.drawStandardText(img, x, y, text, face, col)
	} else {
		// Use scaled rendering for larger text
		efr.drawScaledText(img, x, y, text, face, col, scale)
	}
}

// drawStandardText draws text using standard font rendering
func (efr *EnhancedFontRenderer) drawStandardText(img *image.RGBA, x, y int, text string, face font.Face, col color.RGBA) {
	point := fixed.Point26_6{
		X: fixed.Int26_6(x * 64),
		Y: fixed.Int26_6(y * 64),
	}

	d := &font.Drawer{
		Dst:  img,
		Src:  &image.Uniform{col},
		Face: face,
		Dot:  point,
	}
	d.DrawString(text)
}

// drawScaledText draws text with scaling for larger sizes
func (efr *EnhancedFontRenderer) drawScaledText(img *image.RGBA, x, y int, text string, face font.Face, col color.RGBA, scale float64) {
	// Create a temporary image for the text
	bounds := img.Bounds()
	tempImg := image.NewRGBA(image.Rect(0, 0, bounds.Dx(), bounds.Dy()))

	// Draw text on temporary image
	efr.drawStandardText(tempImg, int(float64(x)/scale), int(float64(y)/scale), text, face, col)

	// Scale and draw onto main image
	efr.drawScaledImage(img, tempImg, x, y, scale)
}

// drawScaledImage draws a scaled version of the source image onto the destination
func (efr *EnhancedFontRenderer) drawScaledImage(dst, src *image.RGBA, x, y int, scale float64) {
	srcBounds := src.Bounds()

	for sy := srcBounds.Min.Y; sy < srcBounds.Max.Y; sy++ {
		for sx := srcBounds.Min.X; sx < srcBounds.Max.X; sx++ {
			srcColor := src.RGBAAt(sx, sy)

			// Skip transparent pixels
			if srcColor.A == 0 {
				continue
			}

			// Draw scaled pixel block
			blockSize := int(scale)
			for dy := 0; dy < blockSize; dy++ {
				for dx := 0; dx < blockSize; dx++ {
					dstX := x + sx*blockSize + dx
					dstY := y + sy*blockSize + dy

					if dstX >= 0 && dstY >= 0 && dstX < dst.Bounds().Dx() && dstY < dst.Bounds().Dy() {
						efr.blendPixel(dst, dstX, dstY, srcColor)
					}
				}
			}
		}
	}
}

// drawTextOutline draws text outline for better readability
func (efr *EnhancedFontRenderer) drawTextOutline(img *image.RGBA, x, y int, text string, face font.Face, outlineColor color.RGBA, scale float64) {
	// Draw outline by rendering text in multiple positions
	offsets := []struct{ dx, dy int }{
		{-1, -1}, {0, -1}, {1, -1},
		{-1, 0}, {1, 0},
		{-1, 1}, {0, 1}, {1, 1},
	}

	for _, offset := range offsets {
		efr.drawTextLayer(img, x+offset.dx, y+offset.dy, text, face, outlineColor, scale)
	}
}

// blendPixel blends a pixel with alpha blending
func (efr *EnhancedFontRenderer) blendPixel(img *image.RGBA, x, y int, newColor color.RGBA) {
	if x < 0 || y < 0 || x >= img.Bounds().Dx() || y >= img.Bounds().Dy() {
		return
	}

	existing := img.RGBAAt(x, y)
	alpha := float64(newColor.A) / 255.0
	invAlpha := 1.0 - alpha

	blended := color.RGBA{
		R: uint8(float64(newColor.R)*alpha + float64(existing.R)*invAlpha),
		G: uint8(float64(newColor.G)*alpha + float64(existing.G)*invAlpha),
		B: uint8(float64(newColor.B)*alpha + float64(existing.B)*invAlpha),
		A: uint8(math.Max(float64(existing.A), float64(newColor.A))),
	}

	img.Set(x, y, blended)
}
