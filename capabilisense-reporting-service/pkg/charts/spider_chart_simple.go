package charts

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"

	"golang.org/x/image/draw"
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/fixed"
)

// SpiderChartData represents the data for a spider chart
type SpiderChartData struct {
	Domains    []DomainData `json:"domains"`
	MaxLevel   int          `json:"max_level"`   // Maximum maturity level (e.g., 5)
	ChartTitle string       `json:"chart_title"` // Title for the chart
}

// DomainData represents a single domain's data
type DomainData struct {
	Name  string  `json:"name"`
	Score float64 `json:"score"`
	Color string  `json:"color,omitempty"` // Optional custom color
}

// SpiderChartConfig holds configuration for chart appearance
type SpiderChartConfig struct {
	Width           int
	Height          int
	CenterX         int
	CenterY         int
	MaxRadius       int
	BackgroundColor color.RGBA
	GridColor       color.RGBA
	TextColor       color.RGBA
	DataColor       color.RGBA
	FillColor       color.RGBA
	LineWidth       float64
	ShowGrid        bool
	ShowLabels      bool
	ShowValues      bool
	// Professional styling options
	UseGradientFill bool
	GradientColors  []color.RGBA
	GridLineWidth   float64
	TitleFontSize   int
	LabelFontSize   int
	ShowShadow      bool
	ShadowColor     color.RGBA
	ShadowOffset    int
	AntiAliasing    bool
	ModernStyle     bool
}

// DefaultSpiderChartConfig returns a default configuration
func DefaultSpiderChartConfig() SpiderChartConfig {
	return SpiderChartConfig{
		Width:           800,
		Height:          800,
		CenterX:         400,
		CenterY:         400,
		MaxRadius:       300,
		BackgroundColor: color.RGBA{255, 255, 255, 255}, // White
		GridColor:       color.RGBA{200, 200, 200, 255}, // Light gray
		TextColor:       color.RGBA{50, 50, 50, 255},    // Dark gray
		DataColor:       color.RGBA{0, 120, 215, 255},   // Blue
		FillColor:       color.RGBA{0, 120, 215, 80},    // Semi-transparent blue
		LineWidth:       2.0,
		ShowGrid:        true,
		ShowLabels:      true,
		ShowValues:      true,
		// Professional styling defaults
		UseGradientFill: false,
		GradientColors:  []color.RGBA{},
		GridLineWidth:   1.0,
		TitleFontSize:   16,
		LabelFontSize:   12,
		ShowShadow:      false,
		ShadowColor:     color.RGBA{0, 0, 0, 50},
		ShadowOffset:    2,
		AntiAliasing:    false,
		ModernStyle:     false,
	}
}

// SpiderChart generates spider charts
type SpiderChart struct {
	config SpiderChartConfig
}

// NewSpiderChart creates a new spider chart generator
func NewSpiderChart(config SpiderChartConfig) *SpiderChart {
	return &SpiderChart{
		config: config,
	}
}

// GenerateChart creates a spider chart and saves it to the specified file
func (sc *SpiderChart) GenerateChart(data SpiderChartData, outputPath string) error {
	// Create image
	img := image.NewRGBA(image.Rect(0, 0, sc.config.Width, sc.config.Height))

	// Fill background
	draw.Draw(img, img.Bounds(), &image.Uniform{sc.config.BackgroundColor}, image.Point{}, draw.Src)

	// Draw chart
	sc.drawChart(img, data)

	// Save to file
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return fmt.Errorf("failed to encode PNG: %w", err)
	}

	return nil
}

// drawChart draws the complete spider chart
func (sc *SpiderChart) drawChart(img *image.RGBA, data SpiderChartData) {
	// Draw title
	if data.ChartTitle != "" {
		sc.drawTitle(img, data.ChartTitle)
	}

	// Draw grid
	if sc.config.ShowGrid {
		sc.drawGrid(img, data)
	}

	// Draw axes
	sc.drawAxes(img, data)

	// Draw labels
	if sc.config.ShowLabels {
		sc.drawLabels(img, data)
	}

	// Draw data
	sc.drawData(img, data)

	// Draw level indicators
	if sc.config.ShowValues {
		sc.drawLevelIndicators(img, data)
	}
}

// drawTitle draws the chart title using basic font
func (sc *SpiderChart) drawTitle(img *image.RGBA, title string) {
	// Simple title drawing - just place text at top center
	titleY := 30
	titleX := sc.config.Width/2 - len(title)*4 // Rough centering
	sc.drawText(img, titleX, titleY, title, sc.config.TextColor)
}

// drawGrid draws the concentric grid lines
func (sc *SpiderChart) drawGrid(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := sc.config.CenterX, sc.config.CenterY

	// Draw concentric circles for each level
	for level := 1; level <= data.MaxLevel; level++ {
		radius := int(float64(sc.config.MaxRadius) * float64(level) / float64(data.MaxLevel))
		sc.drawCircle(img, centerX, centerY, radius, sc.config.GridColor, 1.0)
	}
}

// drawAxes draws the axes from center to each domain
func (sc *SpiderChart) drawAxes(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := sc.config.CenterX, sc.config.CenterY
	numDomains := len(data.Domains)

	for i := 0; i < numDomains; i++ {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2 // Start from top
		endX := centerX + int(float64(sc.config.MaxRadius)*math.Cos(angle))
		endY := centerY + int(float64(sc.config.MaxRadius)*math.Sin(angle))

		sc.drawLine(img, centerX, centerY, endX, endY, sc.config.GridColor, 1.0)
	}
}

// drawLabels draws domain labels around the chart
func (sc *SpiderChart) drawLabels(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := sc.config.CenterX, sc.config.CenterY
	labelRadius := sc.config.MaxRadius + 40
	numDomains := len(data.Domains)

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2

		// Calculate label position
		labelX := centerX + int(float64(labelRadius)*math.Cos(angle))
		labelY := centerY + int(float64(labelRadius)*math.Sin(angle))

		// Adjust text position based on angle for better readability
		textWidth := len(domain.Name) * 8 // Rough estimate for basic font
		if angle > math.Pi/4 && angle < 3*math.Pi/4 {
			labelX -= textWidth / 2 // Bottom labels - center
		} else if angle >= 3*math.Pi/4 || angle <= -3*math.Pi/4 {
			labelX -= textWidth // Left labels - right align
		} else if angle > -3*math.Pi/4 && angle < -math.Pi/4 {
			labelX -= textWidth / 2 // Top labels - center
		}
		// Right labels use default left align

		sc.drawText(img, labelX, labelY, domain.Name, sc.config.TextColor)
	}
}

// drawData draws the actual data polygon with professional styling
func (sc *SpiderChart) drawData(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := sc.config.CenterX, sc.config.CenterY
	numDomains := len(data.Domains)

	if numDomains == 0 {
		return
	}

	// Calculate points
	points := make([]image.Point, numDomains)
	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2
		radius := float64(sc.config.MaxRadius) * domain.Score / float64(data.MaxLevel)

		x := centerX + int(radius*math.Cos(angle))
		y := centerY + int(radius*math.Sin(angle))
		points[i] = image.Point{x, y}
	}

	// Draw shadow if enabled
	if sc.config.ShowShadow {
		shadowPoints := make([]image.Point, numDomains)
		for i, point := range points {
			shadowPoints[i] = image.Point{
				X: point.X + sc.config.ShadowOffset,
				Y: point.Y + sc.config.ShadowOffset,
			}
		}
		sc.fillPolygon(img, shadowPoints, sc.config.ShadowColor)
	}

	// Fill polygon with gradient or solid color
	if sc.config.UseGradientFill && len(sc.config.GradientColors) > 0 {
		sc.fillPolygonWithGradient(img, points, data)
	} else {
		sc.fillPolygon(img, points, sc.config.FillColor)
	}

	// Draw polygon outline with enhanced styling
	lineWidth := sc.config.LineWidth
	if sc.config.ModernStyle {
		lineWidth = math.Max(lineWidth, 2.5)
	}

	for i := 0; i < numDomains; i++ {
		nextI := (i + 1) % numDomains
		sc.drawLine(img, points[i].X, points[i].Y, points[nextI].X, points[nextI].Y,
			sc.config.DataColor, lineWidth)
	}

	// Draw data points with enhanced styling
	pointRadius := 4
	if sc.config.ModernStyle {
		pointRadius = 6
	}
	for _, point := range points {
		// Draw outer ring for modern style
		if sc.config.ModernStyle {
			sc.drawCircle(img, point.X, point.Y, pointRadius+2,
				color.RGBA{255, 255, 255, 255}, 2.0)
		}
		sc.drawCircle(img, point.X, point.Y, pointRadius, sc.config.DataColor, 2.0)
	}
}

// drawLevelIndicators draws level numbers on the chart
func (sc *SpiderChart) drawLevelIndicators(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := sc.config.CenterX, sc.config.CenterY

	// Draw level indicators on the first axis (top)
	for level := 1; level <= data.MaxLevel; level++ {
		radius := float64(sc.config.MaxRadius) * float64(level) / float64(data.MaxLevel)
		y := centerY - int(radius)

		levelText := fmt.Sprintf("%d", level)
		sc.drawText(img, centerX+10, y+5, levelText, sc.config.TextColor)
	}
}

// drawText draws text using basic font
func (sc *SpiderChart) drawText(img *image.RGBA, x, y int, text string, col color.RGBA) {
	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(col),
		Face: basicfont.Face7x13,
		Dot:  fixed.Point26_6{X: fixed.I(x), Y: fixed.I(y)},
	}
	d.DrawString(text)
}

// drawLine draws a line between two points
func (sc *SpiderChart) drawLine(img *image.RGBA, x1, y1, x2, y2 int, col color.RGBA, width float64) {
	// Simple line drawing using Bresenham's algorithm
	dx := abs(x2 - x1)
	dy := abs(y2 - y1)

	var sx, sy int
	if x1 < x2 {
		sx = 1
	} else {
		sx = -1
	}
	if y1 < y2 {
		sy = 1
	} else {
		sy = -1
	}

	err := dx - dy
	x, y := x1, y1

	for {
		// Draw pixel with thickness
		for i := -int(width / 2); i <= int(width/2); i++ {
			for j := -int(width / 2); j <= int(width/2); j++ {
				px, py := x+i, y+j
				if px >= 0 && py >= 0 && px < img.Bounds().Dx() && py < img.Bounds().Dy() {
					img.Set(px, py, col)
				}
			}
		}

		if x == x2 && y == y2 {
			break
		}

		e2 := 2 * err
		if e2 > -dy {
			err -= dy
			x += sx
		}
		if e2 < dx {
			err += dx
			y += sy
		}
	}
}

// drawCircle draws a circle
func (sc *SpiderChart) drawCircle(img *image.RGBA, centerX, centerY, radius int, col color.RGBA, width float64) {
	for angle := 0.0; angle < 2*math.Pi; angle += 0.01 {
		x := centerX + int(float64(radius)*math.Cos(angle))
		y := centerY + int(float64(radius)*math.Sin(angle))

		// Draw with thickness
		for i := -int(width / 2); i <= int(width/2); i++ {
			for j := -int(width / 2); j <= int(width/2); j++ {
				px, py := x+i, y+j
				if px >= 0 && py >= 0 && px < img.Bounds().Dx() && py < img.Bounds().Dy() {
					img.Set(px, py, col)
				}
			}
		}
	}
}

// fillPolygon fills a polygon using scanline algorithm
func (sc *SpiderChart) fillPolygon(img *image.RGBA, points []image.Point, col color.RGBA) {
	if len(points) < 3 {
		return
	}

	// Find bounding box
	minY, maxY := points[0].Y, points[0].Y
	for _, p := range points {
		if p.Y < minY {
			minY = p.Y
		}
		if p.Y > maxY {
			maxY = p.Y
		}
	}

	// Scanline fill
	for y := minY; y <= maxY; y++ {
		intersections := []int{}

		// Find intersections with polygon edges
		for i := 0; i < len(points); i++ {
			j := (i + 1) % len(points)
			p1, p2 := points[i], points[j]

			if (p1.Y <= y && p2.Y > y) || (p2.Y <= y && p1.Y > y) {
				// Calculate intersection x
				x := p1.X + (y-p1.Y)*(p2.X-p1.X)/(p2.Y-p1.Y)
				intersections = append(intersections, x)
			}
		}

		// Sort intersections
		for i := 0; i < len(intersections)-1; i++ {
			for j := i + 1; j < len(intersections); j++ {
				if intersections[i] > intersections[j] {
					intersections[i], intersections[j] = intersections[j], intersections[i]
				}
			}
		}

		// Fill between pairs of intersections
		for i := 0; i < len(intersections)-1; i += 2 {
			for x := intersections[i]; x <= intersections[i+1]; x++ {
				if x >= 0 && y >= 0 && x < img.Bounds().Dx() && y < img.Bounds().Dy() {
					img.Set(x, y, col)
				}
			}
		}
	}
}

// fillPolygonWithGradient fills a polygon with a gradient based on score values
func (sc *SpiderChart) fillPolygonWithGradient(img *image.RGBA, points []image.Point, data SpiderChartData) {
	if len(points) == 0 || len(sc.config.GradientColors) == 0 {
		return
	}

	// Find bounding box
	minX, maxX := points[0].X, points[0].X
	minY, maxY := points[0].Y, points[0].Y
	for _, p := range points {
		if p.X < minX {
			minX = p.X
		}
		if p.X > maxX {
			maxX = p.X
		}
		if p.Y < minY {
			minY = p.Y
		}
		if p.Y > maxY {
			maxY = p.Y
		}
	}

	// Fill polygon with gradient
	for y := minY; y <= maxY; y++ {
		intersections := []int{}

		// Find intersections with polygon edges
		for i := 0; i < len(points); i++ {
			j := (i + 1) % len(points)
			p1, p2 := points[i], points[j]

			if (p1.Y <= y && y < p2.Y) || (p2.Y <= y && y < p1.Y) {
				// Calculate intersection x
				if p2.Y != p1.Y {
					x := p1.X + (y-p1.Y)*(p2.X-p1.X)/(p2.Y-p1.Y)
					intersections = append(intersections, x)
				}
			}
		}

		// Sort intersections
		for i := 0; i < len(intersections)-1; i++ {
			for j := i + 1; j < len(intersections); j++ {
				if intersections[i] > intersections[j] {
					intersections[i], intersections[j] = intersections[j], intersections[i]
				}
			}
		}

		// Fill between pairs of intersections with gradient
		for i := 0; i < len(intersections); i += 2 {
			if i+1 < len(intersections) {
				startX, endX := intersections[i], intersections[i+1]
				for x := startX; x <= endX; x++ {
					if x >= 0 && y >= 0 && x < img.Bounds().Dx() && y < img.Bounds().Dy() {
						// Calculate gradient color based on distance from center
						centerX, centerY := sc.config.CenterX, sc.config.CenterY
						distance := math.Sqrt(float64((x-centerX)*(x-centerX) + (y-centerY)*(y-centerY)))
						maxDistance := float64(sc.config.MaxRadius)

						// Normalize distance to 0-1
						normalizedDistance := math.Min(distance/maxDistance, 1.0)

						// Get gradient color
						gradientColor := sc.getGradientColor(normalizedDistance)
						img.Set(x, y, gradientColor)
					}
				}
			}
		}
	}
}

// getGradientColor returns a color from the gradient based on position (0-1)
func (sc *SpiderChart) getGradientColor(position float64) color.RGBA {
	if len(sc.config.GradientColors) == 0 {
		return sc.config.FillColor
	}

	if len(sc.config.GradientColors) == 1 {
		return sc.config.GradientColors[0]
	}

	// Clamp position to 0-1
	if position < 0 {
		position = 0
	}
	if position > 1 {
		position = 1
	}

	// Calculate which colors to interpolate between
	segmentSize := 1.0 / float64(len(sc.config.GradientColors)-1)
	segmentIndex := int(position / segmentSize)

	if segmentIndex >= len(sc.config.GradientColors)-1 {
		return sc.config.GradientColors[len(sc.config.GradientColors)-1]
	}

	// Interpolate between two colors
	localPosition := (position - float64(segmentIndex)*segmentSize) / segmentSize
	color1 := sc.config.GradientColors[segmentIndex]
	color2 := sc.config.GradientColors[segmentIndex+1]

	return color.RGBA{
		R: uint8(float64(color1.R)*(1-localPosition) + float64(color2.R)*localPosition),
		G: uint8(float64(color1.G)*(1-localPosition) + float64(color2.G)*localPosition),
		B: uint8(float64(color1.B)*(1-localPosition) + float64(color2.B)*localPosition),
		A: uint8(float64(color1.A)*(1-localPosition) + float64(color2.A)*localPosition),
	}
}

// abs returns absolute value
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}
